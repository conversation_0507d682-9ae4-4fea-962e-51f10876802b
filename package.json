{"name": "figma", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@tailwindcss/vite": "^4.1.7", "axios": "^1.9.0", "daisyui": "^5.0.35", "dotenv": "^16.5.0", "keycloak-js": "^26.2.0", "lucide-react": "^0.511.0", "moment": "^2.30.1", "react": "^19.1.0", "react-big-calendar": "^1.19.2", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.0", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "tailwindcss": "^4.1.7", "zod": "^3.25.17", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/moment": "^2.11.29", "@types/react": "^19.1.2", "@types/react-big-calendar": "^1.16.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}