import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { loginWithKeycloak } from "../utils/keycloakService";
import { toast } from "react-toastify";
import { Fa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FaEye, FaEyeSlash } from "react-icons/fa";
import axios from "axios";
import doctorImage from "../../src/assets/images/logos/doctor.png"// adjust path if needed

const Login: React.FC = () => {
  /* ───────────────────────── state + helpers ────────────────────────── */
  const [usernameOrEmail, setUsernameOrEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [facility, setFacility] = useState("");
  const [facilityOptions, setFacilityOptions] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const navigate = useNavigate();

  /* ───────────────────────── fetch facility ─────────────────────────── */
useEffect(() => {
  const controller = new AbortController();
  const debounceTimeout = setTimeout(() => {
    if (!usernameOrEmail.trim()) {
      setFacility("");
      setFacilityOptions([]);
      return;
    }

    axios
      .get(`http://localhost:8080/api/facility/${usernameOrEmail}`, {
        signal: controller.signal,
      })
      .then((res) => {
        const data = res.data;
        if (data?.facility) {
          setFacility(data.facility);
          localStorage.setItem("Facility", data.facility);
          if (!facilityOptions.includes(data.facility)) {
            setFacilityOptions([data.facility]);
          }
        }
      })
      .catch((error) => {
        if (axios.isCancel(error)) return; // ignore aborts
        setFacility("");
        setFacilityOptions([]);
      });
  }, 500);

  return () => {
    clearTimeout(debounceTimeout);
    controller.abort(); // cancel previous request if still running
  };
}, [usernameOrEmail]);


  /* ───────────────────────── handle login ───────────────────────────── */
  const handleLogin = async () => {
    setLoading(true);
    setError("");
    try {
      const tokenData = await loginWithKeycloak(usernameOrEmail, password);
      localStorage.setItem("access_token", tokenData.access_token);
      localStorage.setItem("refresh_token", tokenData.refresh_token);
      localStorage.setItem("loggedInUser", usernameOrEmail);
      localStorage.setItem("selectedFacility", facility);

      toast.success(
        <div className="flex items-start gap-2">
          <FaCheckCircle className="text-green-600 mt-1" />
          <div>
            <strong>Login successful</strong>
            <br />
            Welcome,&nbsp;
            <span className="font-semibold">{usernameOrEmail}</span>
          </div>
        </div>,
        {
          style: {
            backgroundColor: "#f0fdf4",
            border: "1px solid #16a34a",
            color: "#1f2937",
            borderRadius: 8,
            padding: 12,
          },
          icon: false,
          position: "top-right",
          autoClose: 3000,
        }
      );
      navigate("/app");
    } catch (err: any) {
      setError("The username or password is incorrect");
      toast.error(err.message);
    } finally {
      setLoading(false);
    }
  };

  /* ───────────────────────────── UI ─────────────────────────────────── */
  return (
    <div className="min-h-screen flex items-center justify-center bg-[#9dc6ff]">
      {/* card */}
      <div className="relative flex w-[95%] max-w-[950px] rounded-3xl overflow-hidden shadow-xl">
        {/* decorative blobs / rings */}
        <div className="pointer-events-none absolute inset-0">
          <div className="absolute -left-12 -top-12 h-44 w-44 rounded-full border-8 border-[#d0f2d2] opacity-60" />
          <div className="absolute right-10 top-10 h-12 w-12 rounded-full border-4 border-[#75d8d4] opacity-60" />
          <div className="absolute right-24 top-32 h-6 w-6 rounded-full border-2 border-[#ffdca8] opacity-70" />
        </div>

        {/* left panel */}
        <div className="flex w-1/2 flex-col items-center justify-center gap-6 bg-gradient-to-br from-[#e6faf0] via-[#d5f5fa] to-[#c9e9ff] p-10">
          <img
            src={doctorImage}
            alt="Doctor illustration"
            className="w-[220px] select-none"
          />

          <div className="text-center">
            <h1 className="text-5xl font-black tracking-wider text-gray-800">
              HELLO{" "}
              <span className="inline-block text-[#2ecc71] align-[0.15em]">
                !
              </span>
            </h1>
            <p className="mt-3 text-base text-gray-600">
              Please enter your details to continue
            </p>
          </div>
        </div>

        {/* right panel */}
        <div className="flex w-1/2 flex-col gap-6 bg-white px-14 py-12">
          <h2 className="mb-2 text-center text-3xl font-semibold text-[#1862ff]">
            Meghasanjivini
          </h2>

          {/* input group */}
          <div className="space-y-4">
            {/* username */}
            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">
                Username or E-mail
              </label>
              <input
                type="text"
                value={usernameOrEmail}
                onChange={(e) => setUsernameOrEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="w-full rounded-md border border-gray-300 px-4 py-2 outline-none transition focus:border-[#1862ff]"
              />
            </div>

            {/* password */}
            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <div className="relative">
                <input
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="••••••••"
                  className={`w-full rounded-md border px-4 py-2 pr-10 outline-none transition ${
                    error
                      ? "border-red-500 focus:border-red-600"
                      : "border-gray-300 focus:border-[#1862ff]"
                  }`}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword((prev) => !prev)}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500"
                >
                  {showPassword ? <FaEyeSlash /> : <FaEye />}
                </button>
              </div>
              {error && (
                <p className="mt-1 text-xs text-red-500">{error}</p>
              )}
            </div>

            {/* facility */}
            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">
                Facility
              </label>
              <input
                value={facility}
                readOnly
                placeholder="Auto-filled"
                className="w-full cursor-not-allowed rounded-md border bg-gray-100 px-4 py-2 text-gray-800"
              />
            </div>
          </div>

          {/* login button */}
          <button
            onClick={handleLogin}
            disabled={loading}
            className="mt-2 w-full rounded-md bg-gradient-to-r from-[#1693fe] to-[#0047ff] py-2 text-white transition hover:opacity-90 disabled:cursor-not-allowed disabled:opacity-70"
          >
            {loading ? "Logging in…" : "Log in"}
          </button>

          {/* links */}
          <div className="mt-1 flex items-center justify-between text-sm">
            {/* <button className="text-[#12b886] hover:underline">
              Forget Password?
            </button> */}
           <button
  className="text-[#0047ff] hover:underline"
  onClick={() => navigate("/signup")}   
>
  Sign Up
</button>

          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
