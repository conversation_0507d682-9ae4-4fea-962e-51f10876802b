import type { ButtonHTMLAttributes } from "react";

type Props = ButtonHTMLAttributes<HTMLButtonElement> & {
  variant?: "primary" | "outline";
};

export const Button = ({
  children,
  variant = "primary",
  className = "",
  ...props
}: Props) => {
  const base =
    "inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg shadow-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2";

  const style =
    variant === "primary"
      ? "bg-gradient-to-r from-blue-500 to-indigo-600 text-white hover:from-blue-600 hover:to-indigo-700 focus:ring-blue-500"
      : "bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-blue-500";

  return (
    <button {...props} className={`${base} ${style} ${className}`}>
      {children}
    </button>
  );
};
