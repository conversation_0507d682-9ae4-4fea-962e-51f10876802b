import { forwardRef } from "react";
import type { InputHTMLAttributes } from "react";

type Props = InputHTMLAttributes<HTMLInputElement>;

export const Calendar = forwardRef<HTMLInputElement, Props>(({ className = "", ...props }, ref) => (
  <input
    ref={ref}
    type="date"
    {...props}
    className={`w-full px-4 py-2 text-sm rounded-lg border border-gray-300 bg-white shadow-sm 
      focus:ring-2 focus:ring-blue-500 focus:border-transparent 
      transition-all duration-200 ${className}`}
  />
));

Calendar.displayName = "Calendar";
