// import React, { useEffect, useState, useRef } from "react";
// import { Input } from "./Input";
// import { fetchFacilities, fetchFacilityLabel } from "../services/facilityApi";

// interface Facility {
//   hospitalId: string;
//   facilityName: string;
// }

// interface Props {
//   name?: string;
//   value?: string | null;
//   onChange: (value: string) => void;
//   className?: string;
//   error?: string;
//   placeholder?: string;
// }

// const FacilitySelector: React.FC<Props> = ({
//   name = "facilityId",
//   value,
//   onChange,
//   className,
//   error,
//   placeholder = "Search facility...",
// }) => {
//   const [search, setSearch] = useState("");
//   const [facilities, setFacilities] = useState<Facility[]>([]);
//   const [showDropdown, setShowDropdown] = useState(false);
//   const [loading, setLoading] = useState(false);
//   const [noResults, setNoResults] = useState(false);
//   const containerRef = useRef<HTMLDivElement>(null);

//   const handleFetchFacilities = async (query: string) => {
//     setLoading(true);
//     setNoResults(false);
//     try {
//       const data = await fetchFacilities(query);
//       if (Array.isArray(data) && data.length > 0) {
//         setFacilities(data);
//         setShowDropdown(true);
//       } else {
//         setFacilities([]);
//         setShowDropdown(true);
//         setNoResults(true);
//       }
//     } catch (err) {
//       console.error("Facility fetch error:", err);
//       setFacilities([]);
//       setShowDropdown(false);
//     } finally {
//       setLoading(false);
//     }
//   };

//   useEffect(() => {
//     if (search.length >= 1) {
//       const timer = setTimeout(() => handleFetchFacilities(search), 300);
//       return () => clearTimeout(timer);
//     } else {
//       setFacilities([]);
//       setShowDropdown(false);
//       setNoResults(false);
//     }
//   }, [search]);

//   useEffect(() => {
//     if (!value) {
//       setSearch("");
//       return;
//     }

//     const fetchLabel = async () => {
//       try {
//         const label = await fetchFacilityLabel(value);
//         setSearch(label);
//       } catch (err) {
//         console.error("Label fetch error:", err);
//       }
//     };

//     fetchLabel();
//   }, [value]);

//   useEffect(() => {
//     const handleClickOutside = (e: MouseEvent) => {
//       if (
//         containerRef.current &&
//         !containerRef.current.contains(e.target as Node)
//       ) {
//         setShowDropdown(false);
//       }
//     };
//     document.addEventListener("mousedown", handleClickOutside);
//     return () => document.removeEventListener("mousedown", handleClickOutside);
//   }, []);

//   return (
//     <div className={`relative w-full ${className ?? ""}`} ref={containerRef}>
//       <Input
//         type="text"
//         name={name}
//         value={search}
//         placeholder={placeholder}
//         onChange={(e) => setSearch(e.target.value)}
//         onFocus={() => {
//           if (facilities.length > 0 || noResults) setShowDropdown(true);
//         }}
//       />

//       {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
//       {loading && <span className="text-xs mt-1 text-gray-500">Loading...</span>}

//       {showDropdown && facilities.length > 0 && (
//         <ul className="absolute z-50 mt-1 w-full bg-white border rounded max-h-48 overflow-auto shadow">
//           {facilities
//             .filter((fac) => fac.hospitalId !== value) // filter out selected
//             .map((fac) => (
//               <li
//                 key={fac.hospitalId}
//                 onClick={() => {
//                   onChange(fac.hospitalId);
//                   setSearch(fac.facilityName);
//                   setShowDropdown(false);
//                 }}
//                 className="px-4 py-2 hover:bg-blue-100 cursor-pointer text-sm"
//               >
//                 {fac.facilityName}
//               </li>
//             ))}
//         </ul>
//       )}
//     </div>
//   );
// };

// export default FacilitySelector;
import React, { useEffect, useState, useRef } from "react";
import { Input } from "./Input";
import { fetchFacilities, fetchFacilityLabel } from "../services/facilityApi";

interface Facility {
  hospitalId: string;
  facilityName: string;
}

interface Props {
  /** Field name that will appear on the synthetic ChangeEvent */
  name?: string;
  /** Selected facility ID */
  value?: string | null;
  /**
   * Called when a facility is selected.
   * Receives a synthetic `ChangeEvent<HTMLInputElement>` so it can plug
   * straight into form libraries (React Hook Form, Formik, etc.).
   */
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  className?: string;
  error?: string;
  placeholder?: string;
}

const FacilitySelector: React.FC<Props> = ({
  name = "facilityId",
  value,
  onChange,
  className,
  error,
  placeholder = "Search facility...",
}) => {
  const [search, setSearch] = useState("");
  const [facilities, setFacilities] = useState<Facility[]>([]);
  const [showDropdown, setShowDropdown] = useState(false);
  const [loading, setLoading] = useState(false);
  const [noResults, setNoResults] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  /* ------------------------- Helpers ------------------------- */

  const handleFetchFacilities = async (query: string) => {
    setLoading(true);
    setNoResults(false);
    try {
      const data = await fetchFacilities(query);
      if (Array.isArray(data) && data.length > 0) {
        setFacilities(data);
        setShowDropdown(true);
      } else {
        setFacilities([]);
        setShowDropdown(true);
        setNoResults(true);
      }
    } catch (err) {
      console.error("Facility fetch error:", err);
      setFacilities([]);
      setShowDropdown(false);
    } finally {
      setLoading(false);
    }
  };

  /** Emit a synthetic ChangeEvent so consumers don’t need to wrap the callback */
  const emitChange = (newValue: string) => {
    const syntheticEvent = {
      target: { name, value: newValue },
    } as unknown as React.ChangeEvent<HTMLInputElement>;
    onChange(syntheticEvent);
  };

  /* ------------------------- Effects ------------------------- */

  // Debounced search
  useEffect(() => {
    if (search.length >= 1) {
      const timer = setTimeout(() => handleFetchFacilities(search), 300);
      return () => clearTimeout(timer);
    } else {
      setFacilities([]);
      setShowDropdown(false);
      setNoResults(false);
    }
  }, [search]);

  // When an ID is passed in, resolve its label for display
  useEffect(() => {
    if (!value) {
      setSearch("");
      return;
    }
    const fetchLabel = async () => {
      try {
        const label = await fetchFacilityLabel(value);
        setSearch(label);
      } catch (err) {
        console.error("Label fetch error:", err);
      }
    };
    fetchLabel();
  }, [value]);

  // Click-outside handler to close dropdown
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(e.target as Node)) {
        setShowDropdown(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  /* ------------------------- Render ------------------------- */

  return (
    <div className={`relative w-full ${className ?? ""}`} ref={containerRef}>
      <Input
        type="text"
        name={name}
        value={search}
        placeholder={placeholder}
        onChange={(e) => setSearch(e.target.value)}
        onFocus={() => {
          if (facilities.length > 0 || noResults) setShowDropdown(true);
        }}
      />

      {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
      {loading && <span className="text-xs mt-1 text-gray-500">Loading...</span>}

      {showDropdown && facilities.length > 0 && (
        <ul className="absolute z-50 mt-1 w-full bg-white border rounded max-h-48 overflow-auto shadow">
          {facilities
            .filter((fac) => fac.hospitalId !== value) // omit already-selected
            .map((fac) => (
              <li
                key={fac.hospitalId}
                onClick={() => {
                  emitChange(fac.hospitalId);      // ⬅️ notify parent with synthetic event
                  setSearch(fac.facilityName);      // update input label
                  setShowDropdown(false);           // close dropdown
                }}
                className="px-4 py-2 hover:bg-blue-100 cursor-pointer text-sm"
              >
                {fac.facilityName}
              </li>
            ))}
        </ul>
      )}
    </div>
  );
};

export default FacilitySelector;
