import React from "react";

type Props = {
  label: string;
  required?: boolean;
  error?: string;
  children: React.ReactNode;
};

export const FormField = ({ label, required, error, children }: Props) => (
  <div className="w-full space-y-1.5">
    <label className="block text-sm font-medium text-gray-700">
      {label} {required && <span className="text-red-500">*</span>}
    </label>
    {children}
    {error && (
      <p className="text-xs text-red-600 mt-0.5">{error}</p>
    )}
  </div>
);
