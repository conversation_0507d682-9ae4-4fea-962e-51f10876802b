import { forwardRef } from "react";
import type { InputHTMLAttributes } from "react";

type Props = InputHTMLAttributes<HTMLInputElement>;

export const Input = forwardRef<HTMLInputElement, Props>(({ className = "", ...props }, ref) => (
  <input
    ref={ref}
    {...props}
    className={`
      w-full px-4 py-2 text-sm text-gray-900 rounded-lg border border-gray-300 bg-white shadow-sm
      placeholder-gray-400
      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
      disabled:bg-gray-100 disabled:cursor-not-allowed
      transition-all duration-200
      ${className}
    `}
  />
));

Input.displayName = "Input";
