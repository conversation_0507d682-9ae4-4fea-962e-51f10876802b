import React, { useState, useEffect } from 'react';
import { X, User, Calendar, Clock, MapPin, FileText, AlertCircle } from 'lucide-react';
import { Button } from '../../commonfields/Button';
import { AppointmentForm } from './AppointmentForm';
import { getProviders } from '../../services/providerApis';
import { getAppointmentsByProvider } from '../../services/appointmentApis';
import { showSuccess, showError } from '../../utils/toastUtils';
import type { Provider } from '../../types/provider';
import type { Appointment } from '../../types/appointment';

interface AppointmentSidePanelProps {
  isOpen: boolean;
  onClose: () => void;
  selectedDate?: string;
  selectedTime?: string;
  onAppointmentCreated: (appointment: Appointment) => void;
  editingAppointment?: Appointment | null;
}

const AppointmentSidePanel: React.FC<AppointmentSidePanelProps> = ({
  isOpen,
  onClose,
  selectedDate,
  selectedTime,
  onAppointmentCreated,
  editingAppointment
}) => {
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(null);
  const [providerAppointments, setProviderAppointments] = useState<Appointment[]>([]);
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(false);
  const [bookedSlots, setBookedSlots] = useState<string[]>([]);

  useEffect(() => {
    if (isOpen) {
      loadProviders();
    }
  }, [isOpen]);

  useEffect(() => {
    if (selectedProvider) {
      loadProviderAppointments();
    }
  }, [selectedProvider, selectedDate]);

  const loadProviders = async () => {
    try {
      const response = await getProviders({ isActive: true, size: 100 });
      setProviders(response.results || []);
    } catch (error) {
      console.error('Failed to load providers:', error);
      showError('Failed to load providers');
    }
  };

  const loadProviderAppointments = async () => {
    if (!selectedProvider) return;

    setLoading(true);
    try {
      console.log('Loading appointments for provider:', selectedProvider.providerId, 'on date:', selectedDate);

      // Get appointments for the selected date or a range around it
      const dateFrom = selectedDate || new Date().toISOString().split('T')[0];
      const dateTo = selectedDate || new Date().toISOString().split('T')[0];

      const appointments = await getAppointmentsByProvider(
        selectedProvider.providerId,
        dateFrom,
        dateTo
      );

      console.log('Received appointments:', appointments);
      setProviderAppointments(appointments);

      // Extract booked slots for the selected date
      if (selectedDate) {
        const dateAppointments = appointments.filter(apt => {
          // Handle different date formats from API
          const aptDate = apt.appointmentDate.split('T')[0]; // Extract date part
          const matches = aptDate === selectedDate && apt.status !== 'Cancelled';
          console.log(`Appointment ${apt.appointmentId}: date=${aptDate}, selectedDate=${selectedDate}, status=${apt.status}, matches=${matches}`);
          return matches;
        });

        console.log('Filtered appointments for date:', dateAppointments);

        // Extract time slots (handle different time formats)
        const slots = dateAppointments.map(apt => {
          // Handle time format - extract HH:MM from various formats
          let timeSlot = apt.startTime;
          if (apt.startTime.includes('T')) {
            timeSlot = apt.startTime.split('T')[1].substring(0, 5);
          } else if (apt.startTime.includes(':')) {
            timeSlot = apt.startTime.substring(0, 5);
          }
          console.log(`Extracted time slot: ${apt.startTime} -> ${timeSlot}`);
          return timeSlot;
        });

        setBookedSlots(slots);
        console.log(`Found ${slots.length} booked slots for ${selectedDate}:`, slots);
      }
    } catch (error) {
      console.error('Failed to load provider appointments:', error);
      setProviderAppointments([]);
      setBookedSlots([]);
    } finally {
      setLoading(false);
    }
  };

  const handleProviderChange = (providerId: string) => {
    const provider = providers.find(p => p.providerId === providerId);
    setSelectedProvider(provider || null);
  };

  const handleAppointmentSuccess = (appointment: Appointment) => {
    onAppointmentCreated(appointment);
    showSuccess(editingAppointment ? 'Appointment updated successfully' : 'Appointment created successfully');
    onClose();
  };

  const isSlotBooked = (time: string) => {
    return bookedSlots.includes(time);
  };

  const getProviderSchedule = () => {
    if (!selectedProvider || !selectedDate) return [];

    return providerAppointments.filter(apt => {
      // Handle different date formats from API
      const aptDate = apt.appointmentDate.split('T')[0]; // Extract date part
      return aptDate === selectedDate;
    }).sort((a, b) => {
      // Sort by time, handling different time formats
      const timeA = a.startTime.includes('T') ? a.startTime.split('T')[1] : a.startTime;
      const timeB = b.startTime.includes('T') ? b.startTime.split('T')[1] : b.startTime;
      return timeA.localeCompare(timeB);
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      
      <div className="absolute right-0 top-0 h-full w-full max-w-4xl bg-white shadow-xl">
        <div className="flex h-full">
          {/* Main Form Section */}
          <div className="flex-1 flex flex-col">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  {editingAppointment ? 'Edit Appointment' : 'Create New Appointment'}
                </h2>
                {selectedDate && (
                  <p className="text-sm text-gray-600 mt-1">
                    {new Date(selectedDate).toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                    {selectedTime && ` at ${selectedTime}`}
                  </p>
                )}
              </div>
              <Button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600"
              >
                <X size={24} />
              </Button>
            </div>

            {/* Form Content */}
            <div className="flex-1 overflow-y-auto p-6">
              <AppointmentForm
                selectedDate={selectedDate}
                selectedTime={selectedTime}
                onSuccess={handleAppointmentSuccess}
                onProviderChange={handleProviderChange}
                bookedSlots={bookedSlots}
                editingAppointment={editingAppointment}
                isSlotBooked={isSlotBooked}
              />
            </div>
          </div>

          {/* Provider Info & Schedule Section */}
          {selectedProvider && (
            <div className="w-80 border-l border-gray-200 bg-gray-50">
              {/* Provider Profile */}
              <div className="p-6 border-b border-gray-200 bg-white">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <User className="text-blue-600" size={24} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      {selectedProvider.title} {selectedProvider.firstName} {selectedProvider.lastName}
                    </h3>
                    <p className="text-sm text-gray-600">{selectedProvider.specialization}</p>
                    <p className="text-xs text-gray-500">{selectedProvider.department}</p>
                  </div>
                </div>
                
                <div className="mt-4 space-y-2 text-sm">
                  <div className="flex items-center space-x-2">
                    <MapPin size={16} className="text-gray-400" />
                    <span className="text-gray-600">
                      {selectedProvider.department || 'Main Hospital'}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <FileText size={16} className="text-gray-400" />
                    <span className="text-gray-600">
                      License: {selectedProvider.licenseNumber || 'N/A'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Today's Schedule */}
              <div className="p-6">
                <div className="flex items-center space-x-2 mb-4">
                  <Calendar size={20} className="text-gray-600" />
                  <h4 className="font-medium text-gray-900">
                    {selectedDate ? 'Selected Date Schedule' : 'Today\'s Schedule'}
                  </h4>
                </div>

                {loading ? (
                  <div className="text-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="text-sm text-gray-500 mt-2">Loading schedule...</p>
                  </div>
                ) : (
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {getProviderSchedule().length > 0 ? (
                      getProviderSchedule().map((appointment, index) => (
                        <div
                          key={appointment.appointmentId || index}
                          className={`p-3 rounded-lg border ${
                            appointment.status === 'Cancelled' 
                              ? 'bg-red-50 border-red-200' 
                              : appointment.status === 'Completed'
                              ? 'bg-green-50 border-green-200'
                              : 'bg-blue-50 border-blue-200'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <Clock size={14} className="text-gray-500" />
                              <span className="text-sm font-medium">
                                {appointment.startTime.includes('T')
                                  ? appointment.startTime.split('T')[1].substring(0, 5)
                                  : appointment.startTime.substring(0, 5)
                                } - {appointment.endTime.includes('T')
                                  ? appointment.endTime.split('T')[1].substring(0, 5)
                                  : appointment.endTime.substring(0, 5)
                                }
                              </span>
                            </div>
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              appointment.status === 'Cancelled' 
                                ? 'bg-red-100 text-red-800' 
                                : appointment.status === 'Completed'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-blue-100 text-blue-800'
                            }`}>
                              {appointment.status}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mt-1">
                            {appointment.patient?.firstName} {appointment.patient?.lastName}
                          </p>
                          <p className="text-xs text-gray-500">
                            {appointment.type} - {appointment.reason || 'General consultation'}
                          </p>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8">
                        <Calendar className="mx-auto h-12 w-12 text-gray-400 mb-3" />
                        <p className="text-sm text-gray-500">
                          No appointments scheduled
                          {selectedDate && ' for this date'}
                        </p>
                      </div>
                    )}
                  </div>
                )}

                {/* Booked Slots Warning */}
                {bookedSlots.length > 0 && selectedDate && (
                  <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <AlertCircle size={16} className="text-yellow-600" />
                      <span className="text-sm font-medium text-yellow-800">
                        Unavailable Times
                      </span>
                    </div>
                    <div className="mt-2 flex flex-wrap gap-1">
                      {bookedSlots.map((slot, index) => (
                        <span
                          key={index}
                          className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded"
                        >
                          {slot}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AppointmentSidePanel;
