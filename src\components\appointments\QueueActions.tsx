import React, { useState } from "react";
import { 
  <PERSON>, 
  User<PERSON><PERSON><PERSON>, 
  <PERSON>, 
  CheckCircle, 
  XCircle, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>riangle
} from "lucide-react";
import { QueueStatus } from "../../types/queue";
import type { QueueEntry } from "../../types/queue";
import { AppointmentPriority } from "../../types/appointmentenums";
import { updateQueueStatus, removeFromQueue } from "../../services/queueApis";
import { useQueueStore } from "../../store/queueStore";
import { Button } from "../../commonfields/Button";
import { showError, showSuccess } from "../../utils/toastUtils";

interface QueueActionsProps {
  queueEntry: QueueEntry;
  onUpdate?: () => void;
}

export const QueueActions: React.FC<QueueActionsProps> = ({
  queueEntry,
  onUpdate
}) => {
  const [loading, setLoading] = useState(false);
  const { updateQueueStatus: updateStoreStatus, removeFromQueue: removeFromStore } = useQueueStore();

  const handleStatusUpdate = async (newStatus: QueueStatus, notes?: string) => {
    setLoading(true);
    try {
      const result = await updateQueueStatus(
        queueEntry.serviceType,
        queueEntry.queueId,
        newStatus,
        notes
      );

      if (result.success) {
        updateStoreStatus(queueEntry.queueId, newStatus);
        showSuccess(`Patient status updated to ${newStatus}`);
        onUpdate?.();
      } else {
        showError(result.error || "Failed to update status");
      }
    } catch (error) {
      console.error("Failed to update queue status:", error);
      showError("Failed to update patient status");
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveFromQueue = async (reason: string) => {
    setLoading(true);
    try {
      const result = await removeFromQueue(
        queueEntry.serviceType,
        queueEntry.queueId,
        reason
      );

      if (result.success) {
        removeFromStore(queueEntry.queueId);
        showSuccess("Patient removed from queue");
        onUpdate?.();
      } else {
        showError(result.error || "Failed to remove from queue");
      }
    } catch (error) {
      console.error("Failed to remove from queue:", error);
      showError("Failed to remove patient from queue");
    } finally {
      setLoading(false);
    }
  };

  const handleCallPatient = () => {
    handleStatusUpdate(QueueStatus.Called, "Patient called for service");
  };

  const handleStartService = () => {
    handleStatusUpdate(QueueStatus.InService, "Service started");
  };

  const handleCompleteService = () => {
    handleStatusUpdate(QueueStatus.Completed, "Service completed successfully");
  };

  const handleMarkNoShow = () => {
    handleStatusUpdate(QueueStatus.NoShow, "Patient did not show up");
  };

  const handleSkipPatient = () => {
    handleStatusUpdate(QueueStatus.Skipped, "Patient skipped temporarily");
  };

  const callPatient = () => {
    if (queueEntry.patient?.mobileNumber) {
      window.open(`tel:${queueEntry.patient.mobileNumber}`);
    }
  };

  const getStatusColor = (status: QueueStatus) => {
    switch (status) {
      case QueueStatus.Waiting:
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case QueueStatus.Called:
        return "bg-blue-100 text-blue-800 border-blue-200";
      case QueueStatus.InService:
        return "bg-green-100 text-green-800 border-green-200";
      case QueueStatus.Completed:
        return "bg-gray-100 text-gray-800 border-gray-200";
      case QueueStatus.NoShow:
        return "bg-red-100 text-red-800 border-red-200";
      case QueueStatus.Skipped:
        return "bg-orange-100 text-orange-800 border-orange-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getPriorityColor = (priority: AppointmentPriority) => {
    switch (priority) {
      case AppointmentPriority.Emergency:
        return "bg-red-500 text-white";
      case AppointmentPriority.Urgent:
        return "bg-orange-500 text-white";
      case AppointmentPriority.High:
        return "bg-yellow-500 text-white";
      case AppointmentPriority.Normal:
        return "bg-blue-500 text-white";
      case AppointmentPriority.Low:
        return "bg-gray-500 text-white";
      default:
        return "bg-gray-500 text-white";
    }
  };

  const getAvailableActions = () => {
    switch (queueEntry.status) {
      case QueueStatus.Waiting:
        return [
          {
            label: "Call Patient",
            icon: <UserCheck size={16} />,
            action: handleCallPatient,
            color: "bg-blue-600 hover:bg-blue-700 text-white"
          },
          {
            label: "Skip",
            icon: <SkipForward size={16} />,
            action: handleSkipPatient,
            color: "bg-orange-600 hover:bg-orange-700 text-white"
          },
          {
            label: "No Show",
            icon: <XCircle size={16} />,
            action: handleMarkNoShow,
            color: "bg-red-600 hover:bg-red-700 text-white"
          }
        ];
      
      case QueueStatus.Called:
        return [
          {
            label: "Start Service",
            icon: <Play size={16} />,
            action: handleStartService,
            color: "bg-green-600 hover:bg-green-700 text-white"
          },
          {
            label: "No Show",
            icon: <XCircle size={16} />,
            action: handleMarkNoShow,
            color: "bg-red-600 hover:bg-red-700 text-white"
          }
        ];
      
      case QueueStatus.InService:
        return [
          {
            label: "Complete",
            icon: <CheckCircle size={16} />,
            action: handleCompleteService,
            color: "bg-green-600 hover:bg-green-700 text-white"
          }
        ];
      
      default:
        return [];
    }
  };

  const availableActions = getAvailableActions();

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      {/* Patient Info Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <div className="text-2xl font-bold text-gray-900">
            #{queueEntry.queueNumber}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-800">
              {queueEntry.patientName}
            </h3>
            <p className="text-sm text-gray-600">
              {queueEntry.serviceType} • {queueEntry.patient?.age} years • {queueEntry.patient?.gender}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getPriorityColor(queueEntry.priority)}`}>
            {queueEntry.priority}
          </span>
          <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(queueEntry.status)}`}>
            {queueEntry.status}
          </span>
        </div>
      </div>

      {/* Time Information */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="text-center p-3 bg-gray-50 rounded-lg">
          <div className="text-sm text-gray-600">Joined Queue</div>
          <div className="font-medium text-gray-900">
            {new Date(queueEntry.joinedAt).toLocaleTimeString()}
          </div>
        </div>
        
        <div className="text-center p-3 bg-gray-50 rounded-lg">
          <div className="text-sm text-gray-600">Estimated Wait</div>
          <div className="font-medium text-gray-900">
            {queueEntry.estimatedWaitTime} min
          </div>
        </div>
        
        <div className="text-center p-3 bg-gray-50 rounded-lg">
          <div className="text-sm text-gray-600">Estimated Service</div>
          <div className="font-medium text-gray-900">
            {queueEntry.estimatedServiceTime}
          </div>
        </div>
      </div>

      {/* Contact Information */}
      {queueEntry.patient?.mobileNumber && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-blue-700">Contact Number</div>
              <div className="font-medium text-blue-900">
                {queueEntry.patient.mobileNumber}
              </div>
            </div>
            <Button
              onClick={callPatient}
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
            >
              <Phone size={16} />
              <span>Call</span>
            </Button>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      {availableActions.length > 0 && (
        <div className="space-y-4">
          <h4 className="font-medium text-gray-800">Available Actions</h4>
          <div className="flex flex-wrap gap-3">
            {availableActions.map((action, index) => (
              <Button
                key={index}
                onClick={action.action}
                disabled={loading}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${action.color}`}
              >
                {action.icon}
                <span>{action.label}</span>
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Emergency Priority Alert */}
      {(queueEntry.priority === AppointmentPriority.Emergency || queueEntry.priority === AppointmentPriority.Urgent) && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="text-red-500" size={20} />
            <div>
              <div className="font-medium text-red-800">High Priority Patient</div>
              <div className="text-sm text-red-700">
                This patient requires immediate attention due to {queueEntry.priority.toLowerCase()} priority.
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Notes Section */}
      {queueEntry.notes && (
        <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <div className="text-sm text-gray-600 mb-1">Notes:</div>
          <div className="text-gray-800">{queueEntry.notes}</div>
        </div>
      )}

      {/* Provider Information */}
      {queueEntry.provider && (
        <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
          <div className="text-sm text-green-700 mb-1">Assigned Provider:</div>
          <div className="font-medium text-green-900">
            {queueEntry.provider.title} {queueEntry.provider.firstName} {queueEntry.provider.lastName}
          </div>
          <div className="text-sm text-green-700">
            {queueEntry.provider.specialization}
          </div>
        </div>
      )}
    </div>
  );
};
