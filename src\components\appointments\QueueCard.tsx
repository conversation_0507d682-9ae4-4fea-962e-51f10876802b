import React from "react";
import { 
  Users, 
  Clock, 
  Activity, 
  AlertCircle, 
  CheckCircle, 
  Stethoscope,
  FlaskConical,
  Pill,
  FileText,
  UserPlus
} from "lucide-react";
import { ServiceType, QueueStatus } from "../../types/queue";
import type { QueueSummary } from "../../types/queue";
import { AppointmentPriority } from "../../types/appointmentenums";

interface QueueCardProps {
  serviceType: ServiceType;
  queue?: QueueSummary;
  onViewDetails?: () => void;
}

export const QueueCard: React.FC<QueueCardProps> = ({
  serviceType,
  queue,
  onViewDetails
}) => {
  const getServiceIcon = (service: ServiceType) => {
    switch (service) {
      case ServiceType.Emergency:
        return <AlertCircle className="text-red-500" size={24} />;
      case ServiceType.Consultation:
        return <Stethoscope className="text-blue-500" size={24} />;
      case ServiceType.Laboratory:
        return <FlaskConical className="text-green-500" size={24} />;
      case ServiceType.Pharmacy:
        return <Pill className="text-purple-500" size={24} />;
      case ServiceType.Diagnostic:
        return <Activity className="text-orange-500" size={24} />;
      case ServiceType.Radiology:
        return <FileText className="text-indigo-500" size={24} />;
      case ServiceType.Registration:
        return <UserPlus className="text-teal-500" size={24} />;
      case ServiceType.Procedure:
        return <CheckCircle className="text-cyan-500" size={24} />;
      default:
        return <Clock className="text-gray-500" size={24} />;
    }
  };

  const getServiceColor = (service: ServiceType) => {
    switch (service) {
      case ServiceType.Emergency:
        return "border-red-200 bg-red-50";
      case ServiceType.Consultation:
        return "border-blue-200 bg-blue-50";
      case ServiceType.Laboratory:
        return "border-green-200 bg-green-50";
      case ServiceType.Pharmacy:
        return "border-purple-200 bg-purple-50";
      case ServiceType.Diagnostic:
        return "border-orange-200 bg-orange-50";
      case ServiceType.Radiology:
        return "border-indigo-200 bg-indigo-50";
      case ServiceType.Registration:
        return "border-teal-200 bg-teal-50";
      case ServiceType.Procedure:
        return "border-cyan-200 bg-cyan-50";
      default:
        return "border-gray-200 bg-gray-50";
    }
  };

  const getWaitTimeColor = (waitTime: number) => {
    if (waitTime <= 10) return "text-green-600";
    if (waitTime <= 30) return "text-yellow-600";
    return "text-red-600";
  };

  const getQueueLengthColor = (length: number) => {
    if (length === 0) return "text-gray-500";
    if (length <= 5) return "text-green-600";
    if (length <= 15) return "text-yellow-600";
    return "text-red-600";
  };

  const waitingCount = queue?.queue.filter(entry => 
    entry.status === QueueStatus.Waiting || entry.status === QueueStatus.Called
  ).length || 0;

  const emergencyCount = queue?.queue.filter(entry => 
    entry.priority === AppointmentPriority.Emergency || entry.priority === AppointmentPriority.Urgent
  ).length || 0;

  return (
    <div 
      className={`relative bg-white rounded-lg shadow-sm border-2 transition-all duration-200 hover:shadow-md cursor-pointer ${getServiceColor(serviceType)}`}
      onClick={onViewDetails}
    >
      {/* Emergency Badge */}
      {emergencyCount > 0 && (
        <div className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">
          {emergencyCount}
        </div>
      )}

      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            {getServiceIcon(serviceType)}
            <h3 className="text-lg font-semibold text-gray-800">
              {serviceType}
            </h3>
          </div>
          
          {queue && (
            <div className="text-right">
              <div className={`text-2xl font-bold ${getQueueLengthColor(waitingCount)}`}>
                {waitingCount}
              </div>
              <div className="text-xs text-gray-500">in queue</div>
            </div>
          )}
        </div>

        {/* Queue Status */}
        {queue ? (
          <div className="space-y-3">
            {/* Currently Serving */}
            {queue.currentlyServing ? (
              <div className="bg-white rounded-lg p-3 border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900">Now Serving</p>
                    <p className="text-xs text-gray-600">
                      #{queue.currentlyServing.queueEntry.queueNumber} - {queue.currentlyServing.queueEntry.patientName}
                    </p>
                  </div>
                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                </div>
              </div>
            ) : (
              <div className="bg-white rounded-lg p-3 border border-gray-200">
                <div className="flex items-center justify-center text-gray-500">
                  <Clock size={16} className="mr-2" />
                  <span className="text-sm">No active service</span>
                </div>
              </div>
            )}

            {/* Wait Time */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Clock size={16} className="text-gray-400" />
                <span className="text-sm text-gray-600">Est. Wait:</span>
              </div>
              <span className={`text-sm font-medium ${getWaitTimeColor(queue.estimatedWaitTime)}`}>
                ~{queue.estimatedWaitTime} min
              </span>
            </div>

            {/* Average Service Time */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Activity size={16} className="text-gray-400" />
                <span className="text-sm text-gray-600">Avg. Service:</span>
              </div>
              <span className="text-sm font-medium text-gray-700">
                {queue.averageServiceTime} min
              </span>
            </div>

            {/* Peak Hours Indicator */}
            {queue.peakHours.length > 0 && (
              <div className="mt-3 pt-3 border-t border-gray-200">
                <div className="flex items-center space-x-2">
                  <AlertCircle size={14} className="text-yellow-500" />
                  <span className="text-xs text-gray-600">
                    Peak: {queue.peakHours.join(", ")}
                  </span>
                </div>
              </div>
            )}

            {/* Last Updated */}
            <div className="text-xs text-gray-500 text-center">
              Updated: {new Date(queue.lastUpdated).toLocaleTimeString()}
            </div>
          </div>
        ) : (
          <div className="space-y-3">
            <div className="bg-white rounded-lg p-3 border border-gray-200">
              <div className="flex items-center justify-center text-gray-500">
                <Users size={16} className="mr-2" />
                <span className="text-sm">No queue data</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between text-gray-400">
              <span className="text-sm">Service offline</span>
              <Clock size={16} />
            </div>
          </div>
        )}

        {/* Action Button */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onViewDetails?.();
            }}
            className="w-full text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors"
          >
            View Details →
          </button>
        </div>
      </div>
    </div>
  );
};
