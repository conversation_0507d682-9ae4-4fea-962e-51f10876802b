import React from "react";
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>, 
  <PERSON>, 
  TrendingUp, 
  TrendingDown,
  Star,
  AlertTriangle,
  CheckCircle
} from "lucide-react";
import { ServiceType } from "../../types/queue";
import type { QueueStatistics } from "../../types/queue";

interface QueueStatsDashboardProps {
  statistics: QueueStatistics;
}

export const QueueStatsDashboard: React.FC<QueueStatsDashboardProps> = ({
  statistics
}) => {
  const getServiceIcon = (serviceType: ServiceType) => {
    switch (serviceType) {
      case ServiceType.Emergency:
        return <AlertTriangle className="text-red-500" size={20} />;
      case ServiceType.Consultation:
        return <Users className="text-blue-500" size={20} />;
      case ServiceType.Laboratory:
        return <CheckCircle className="text-green-500" size={20} />;
      default:
        return <Clock className="text-gray-500" size={20} />;
    }
  };

  const getWaitTimeColor = (waitTime: number) => {
    if (waitTime <= 15) return "text-green-600";
    if (waitTime <= 30) return "text-yellow-600";
    return "text-red-600";
  };

  const getPerformanceColor = (value: number, threshold: { good: number; fair: number }) => {
    if (value <= threshold.good) return "text-green-600";
    if (value <= threshold.fair) return "text-yellow-600";
    return "text-red-600";
  };

  const maxHourlyPatients = Math.max(...statistics.hourlyStats.map(stat => stat.patientsServed));

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Processed */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Processed</p>
              <p className="text-2xl font-bold text-gray-900">{statistics.totalProcessed}</p>
            </div>
            <div className="bg-blue-100 p-3 rounded-full">
              <CheckCircle className="text-blue-600" size={24} />
            </div>
          </div>
          <div className="mt-4 text-sm text-gray-600">
            Today's completed services
          </div>
        </div>

        {/* Current in Queue */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Current in Queue</p>
              <p className="text-2xl font-bold text-gray-900">{statistics.currentInQueue}</p>
            </div>
            <div className="bg-yellow-100 p-3 rounded-full">
              <Users className="text-yellow-600" size={24} />
            </div>
          </div>
          <div className="mt-4 text-sm text-gray-600">
            Patients waiting across all services
          </div>
        </div>

        {/* Average Wait Time */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Wait Time</p>
              <p className={`text-2xl font-bold ${getWaitTimeColor(statistics.averageWaitTime)}`}>
                {statistics.averageWaitTime} min
              </p>
            </div>
            <div className="bg-purple-100 p-3 rounded-full">
              <Clock className="text-purple-600" size={24} />
            </div>
          </div>
          <div className="mt-4 text-sm text-gray-600">
            Overall facility average
          </div>
        </div>

        {/* Patient Satisfaction */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Satisfaction Score</p>
              <p className="text-2xl font-bold text-gray-900">
                {statistics.patientSatisfactionScore?.toFixed(1) || 'N/A'}
              </p>
            </div>
            <div className="bg-green-100 p-3 rounded-full">
              <Star className="text-green-600" size={24} />
            </div>
          </div>
          <div className="mt-4 text-sm text-gray-600">
            Out of 5.0 rating
          </div>
        </div>
      </div>

      {/* Service Statistics */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-6">Service Performance</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Object.entries(statistics.serviceStats).map(([serviceType, stats]) => (
            <div key={serviceType} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-3 mb-4">
                {getServiceIcon(serviceType as ServiceType)}
                <h4 className="font-medium text-gray-800">{serviceType}</h4>
              </div>
              
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Processed:</span>
                  <span className="font-medium">{stats.totalProcessed}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">In Queue:</span>
                  <span className="font-medium">{stats.currentInQueue}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Avg Wait:</span>
                  <span className={`font-medium ${getWaitTimeColor(stats.averageWaitTime)}`}>
                    {stats.averageWaitTime} min
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Avg Service:</span>
                  <span className="font-medium">{stats.averageServiceTime} min</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Hourly Statistics Chart */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-800">Hourly Activity</h3>
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded"></div>
              <span>Patients Served</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-yellow-500 rounded"></div>
              <span>Queue Length</span>
            </div>
          </div>
        </div>
        
        <div className="space-y-4">
          {statistics.hourlyStats.map((stat, index) => (
            <div key={stat.hour} className="flex items-center space-x-4">
              <div className="w-16 text-sm font-medium text-gray-700">
                {stat.hour}
              </div>
              
              {/* Patients Served Bar */}
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <div className="flex-1 bg-gray-200 rounded-full h-4 relative">
                    <div 
                      className="bg-blue-500 h-4 rounded-full transition-all duration-300"
                      style={{ width: `${(stat.patientsServed / maxHourlyPatients) * 100}%` }}
                    ></div>
                    <div className="absolute inset-0 flex items-center justify-center text-xs font-medium text-white">
                      {stat.patientsServed}
                    </div>
                  </div>
                  <div className="w-20 text-sm text-gray-600">
                    {stat.queueLength} in queue
                  </div>
                </div>
              </div>
              
              {/* Wait Time */}
              <div className={`w-20 text-sm font-medium text-right ${getWaitTimeColor(stat.averageWaitTime)}`}>
                {stat.averageWaitTime} min
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Peak Hours and Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Peak Hours */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Peak Hours</h3>
          <div className="space-y-3">
            {statistics.peakHours.map((hour, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="text-red-500" size={16} />
                  <span className="font-medium text-gray-800">{hour}</span>
                </div>
                <span className="text-sm text-red-600">High Volume</span>
              </div>
            ))}
          </div>
          
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="text-blue-500" size={16} />
              <span className="text-sm text-blue-700">
                Consider additional staffing during peak hours
              </span>
            </div>
          </div>
        </div>

        {/* Performance Metrics */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Performance Metrics</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">No-Show Rate</span>
              <span className={`font-medium ${getPerformanceColor(statistics.noShowRate, { good: 5, fair: 10 })}`}>
                {statistics.noShowRate}%
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Average Service Time</span>
              <span className="font-medium text-gray-800">
                {statistics.averageServiceTime} min
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Total Queue Length</span>
              <span className="font-medium text-gray-800">
                {statistics.currentInQueue} patients
              </span>
            </div>
            
            {statistics.patientSatisfactionScore && (
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Patient Satisfaction</span>
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-gray-800">
                    {statistics.patientSatisfactionScore.toFixed(1)}/5.0
                  </span>
                  <Star className="text-yellow-500" size={16} />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Last Updated */}
      <div className="text-center text-sm text-gray-500">
        Last updated: {new Date().toLocaleString()}
      </div>
    </div>
  );
};
