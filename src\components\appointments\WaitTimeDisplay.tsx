import React, { useState, useEffect } from "react";
import { Clock, TrendingUp, TrendingDown, Minus, AlertTriangle } from "lucide-react";
import { ServiceType } from "../../types/queue";
import type { WaitTimeEstimation } from "../../types/queue";
import { AppointmentPriority } from "../../types/appointmentenums";
import { getWaitTimeEstimation } from "../../services/queueApis";
import { useQueueStore } from "../../store/queueStore";

interface WaitTimeDisplayProps {
  serviceType: ServiceType;
  facilityId: string;
  priority?: AppointmentPriority;
  showDetails?: boolean;
}

export const WaitTimeDisplay: React.FC<WaitTimeDisplayProps> = ({
  serviceType,
  facilityId,
  priority,
  showDetails = true
}) => {
  const [waitTime, setWaitTime] = useState<WaitTimeEstimation | null>(null);
  const [loading, setLoading] = useState(false);
  const [previousWaitTime, setPreviousWaitTime] = useState<number | null>(null);

  const { waitTimeEstimations, setWaitTimeEstimations } = useQueueStore();

  useEffect(() => {
    loadWaitTime();
    
    // Set up auto-refresh every 2 minutes
    const interval = setInterval(loadWaitTime, 120000);
    return () => clearInterval(interval);
  }, [serviceType, facilityId, priority]);

  const loadWaitTime = async () => {
    setLoading(true);
    try {
      const estimation = await getWaitTimeEstimation(serviceType, facilityId, priority);
      
      // Store previous wait time for trend calculation
      if (waitTime) {
        setPreviousWaitTime(waitTime.estimatedWaitTime);
      }
      
      setWaitTime(estimation);
      
      // Update store
      setWaitTimeEstimations({
        ...waitTimeEstimations,
        [serviceType]: estimation
      });
    } catch (error) {
      console.error("Failed to load wait time:", error);
    } finally {
      setLoading(false);
    }
  };

  const getWaitTimeColor = (time: number) => {
    if (time <= 10) return "text-green-600";
    if (time <= 30) return "text-yellow-600";
    return "text-red-600";
  };

  const getWaitTimeBackground = (time: number) => {
    if (time <= 10) return "bg-green-50 border-green-200";
    if (time <= 30) return "bg-yellow-50 border-yellow-200";
    return "bg-red-50 border-red-200";
  };

  const getTrendIcon = () => {
    if (!previousWaitTime || !waitTime) return <Minus size={16} className="text-gray-400" />;
    
    const difference = waitTime.estimatedWaitTime - previousWaitTime;
    if (difference > 2) return <TrendingUp size={16} className="text-red-500" />;
    if (difference < -2) return <TrendingDown size={16} className="text-green-500" />;
    return <Minus size={16} className="text-gray-400" />;
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return "text-green-600";
    if (confidence >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const formatTime = (minutes: number) => {
    if (minutes < 60) return `${minutes} min`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  if (loading && !waitTime) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600 mr-3"></div>
          <span className="text-gray-600">Loading wait time...</span>
        </div>
      </div>
    );
  }

  if (!waitTime) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-center text-gray-500">
          <AlertTriangle size={20} className="mr-2" />
          <span>Wait time data unavailable</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Main Wait Time Display */}
      <div className={`rounded-lg border-2 p-6 ${getWaitTimeBackground(waitTime.estimatedWaitTime)}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Clock size={24} className={getWaitTimeColor(waitTime.estimatedWaitTime)} />
            <div>
              <h3 className="text-lg font-semibold text-gray-800">Estimated Wait Time</h3>
              <p className="text-sm text-gray-600">{serviceType} Service</p>
            </div>
          </div>
          
          <div className="text-right">
            <div className={`text-3xl font-bold ${getWaitTimeColor(waitTime.estimatedWaitTime)}`}>
              {formatTime(waitTime.estimatedWaitTime)}
            </div>
            <div className="flex items-center space-x-1 text-sm text-gray-600">
              {getTrendIcon()}
              <span>Queue position: #{waitTime.queuePosition}</span>
            </div>
          </div>
        </div>

        {/* Additional Info */}
        <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-800">{waitTime.patientsAhead}</div>
            <div className="text-xs text-gray-600">Patients Ahead</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-800">{waitTime.averageServiceTime} min</div>
            <div className="text-xs text-gray-600">Avg Service Time</div>
          </div>
          <div className="text-center">
            <div className={`text-lg font-semibold ${getConfidenceColor(waitTime.confidence)}`}>
              {waitTime.confidence}%
            </div>
            <div className="text-xs text-gray-600">Confidence</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-800">
              {new Date(waitTime.lastUpdated).toLocaleTimeString()}
            </div>
            <div className="text-xs text-gray-600">Last Updated</div>
          </div>
        </div>
      </div>

      {/* Priority-based Wait Times */}
      {showDetails && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h4 className="text-lg font-semibold text-gray-800 mb-4">Wait Time by Priority</h4>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            {Object.entries(waitTime.currentWaitTime).map(([priorityLevel, time]) => (
              <div
                key={priorityLevel}
                className={`p-3 rounded-lg border ${
                  priority === priorityLevel ? 'border-indigo-300 bg-indigo-50' : 'border-gray-200 bg-gray-50'
                }`}
              >
                <div className="text-center">
                  <div className={`text-lg font-bold ${getWaitTimeColor(time)}`}>
                    {formatTime(time)}
                  </div>
                  <div className="text-xs text-gray-600 mt-1">{priorityLevel}</div>
                  {priority === priorityLevel && (
                    <div className="text-xs text-indigo-600 font-medium mt-1">Your Priority</div>
                  )}
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-4 text-xs text-gray-500 text-center">
            * Wait times are estimates based on current queue and historical data
          </div>
        </div>
      )}

      {/* Real-time Updates Indicator */}
      <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
        <div className={`w-2 h-2 rounded-full ${loading ? 'bg-yellow-500 animate-pulse' : 'bg-green-500'}`}></div>
        <span>{loading ? 'Updating...' : 'Live data'}</span>
        <span>•</span>
        <span>Updates every 2 minutes</span>
      </div>
    </div>
  );
};
