import { useState, useEffect, useRef } from "react";
import { <PERSON>, Menu } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import SirobiltImage from "../../assets/images/logos/sirobilt.png";
import { toast } from "react-toastify";
import { FaCheckCircle } from "react-icons/fa";
import { useRoles } from "../../hooks/useRoles";

type Props = {
  toggleSidebar: () => void;
  onRegisterClick: () => void;
};

export const Navbar = ({ toggleSidebar, onRegisterClick }: Props) => {
  const [isQuickActionsOpen, setIsQuickActionsOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [username, setUsername] = useState<string | null>(null);
  const navigate = useNavigate();
  const quickActionsRef = useRef<HTMLDivElement | null>(null);
  const profileDropdownRef = useRef<HTMLDivElement | null>(null);
  const { hasRole } = useRoles();

  useEffect(() => {
    const storedUsername = localStorage.getItem("loggedInUser");
    setUsername(storedUsername);
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      if (
        quickActionsRef.current &&
        !quickActionsRef.current.contains(target)
      ) {
        setIsQuickActionsOpen(false);
      }
      if (
        profileDropdownRef.current &&
        !profileDropdownRef.current.contains(target)
      ) {
        setIsProfileOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleLogout = () => {
    localStorage.removeItem("loggedInUser");
    navigate("/login");

    setTimeout(() => {
      toast.info(
        <div className="flex items-start gap-2">
          <FaCheckCircle className="text-blue-600 mt-1" />
          <div>
            <strong>Logout successful</strong>
            <br />
            You have been logged out.
          </div>
        </div>,
        {
          style: {
            backgroundColor: "#eff6ff",
            border: "1px solid #3b82f6",
            color: "#1f2937",
            borderRadius: "8px",
            padding: "12px",
          },
          icon: false,
          position: "top-right",
          autoClose: 3000,
        }
      );
    }, 100);
  };

  return (
    <div className="flex items-center justify-between px-4 py-2 bg-white shadow-sm w-full h-16">
      {/* Left Section */}
      <div className="flex items-center gap-4">
        <button className="block md:hidden" onClick={toggleSidebar}>
          <Menu className="w-6 h-6 text-gray-700" />
        </button>

        <Link to="/" className="flex items-center space-x-2">
          <img src={SirobiltImage} alt="Logo" className="h-8 w-auto" />
        </Link>

        <div className="hidden md:flex items-center gap-2 ml-4">
          <select
            className="border px-2 py-1.5 rounded-md text-sm bg-white text-black"
            onChange={(e) => {
              if (e.target.value === "Patients") navigate("/list");
              if (e.target.value === "All") navigate("/");
            }}
          >
            <option value="All">Home</option>
            <option value="Patients">Patients</option>
          </select>

          <div className="relative">
            <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </span>
            <input
              type="text"
              placeholder="Search"
              className="pl-7 pr-8 py-1.5 border rounded-md text-sm text-black"
            />
            <span className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-4.35-4.35M10 18a8 8 0 100-16 8 8 0 000 16z" />
              </svg>
            </span>
          </div>
        </div>
      </div>

      {/* Right Section */}
      <div className="flex items-center gap-4">
        {/* Quick Actions */}
        <div className="relative" ref={quickActionsRef}>
          <button
            onClick={() => setIsQuickActionsOpen((prev) => !prev)}
            className="flex items-center gap-2 px-4 py-1.5 border border-purple-600 text-purple-600 rounded-full cursor-pointer text-base font-medium bg-white hover:bg-purple-50 focus:outline-none focus:ring-2 focus:ring-purple-400"
          >
            Quick Actions
            <svg className="w-4 h-4" fill="none" stroke="currentColor" strokeWidth={2} viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          {isQuickActionsOpen && (
            <ul className="absolute right-0 mt-2 w-56 py-2 bg-white text-black rounded-lg shadow-lg z-50 border border-gray-200">
              {(hasRole("admin") || hasRole("receptionist")) && (
                <li>
                  <button
                    type="button"
                    onClick={() => {
                      onRegisterClick();
                      setIsQuickActionsOpen(false);
                    }}
                    className="w-full text-left px-4 py-2 hover:bg-gray-100"
                  >
                    Register Patient
                  </button>
                </li>
              )}
              <li>
                <Link to="/appointments" onClick={() => setIsQuickActionsOpen(false)} className="block px-4 py-2 hover:bg-gray-100">
                  Book Appointment
                </Link>
              </li>
              <li>
                <Link to="/queue" onClick={() => setIsQuickActionsOpen(false)} className="block px-4 py-2 hover:bg-gray-100">
                  Queue Management
                </Link>
              </li>
            </ul>
          )}
        </div>

        {/* Notifications */}
        <Bell className="w-5 h-5 text-gray-700 cursor-pointer" />

        {/* Avatar */}
        <div className="relative" ref={profileDropdownRef}>
          <div
            onClick={() => setIsProfileOpen((prev) => !prev)}
            className="avatar w-10 h-10 rounded-full ring ring-blue-400 ring-offset-2 cursor-pointer"
          >
            <div className="relative w-full h-full">
              <img
                src="https://randomuser.me/api/portraits/women/44.jpg"
                alt="User Profile"
                className="rounded-full object-cover w-full h-full"
              />
              <div className="absolute top-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
            </div>
          </div>

          {isProfileOpen && (
            <ul className="absolute right-0 mt-2 w-44 py-2 bg-black text-white rounded-lg shadow-lg z-50">
              {username && (
                <li className="px-4 py-2 text-sm text-gray-300 cursor-default">
                  Logged in as{" "}
                  <span className="font-semibold text-purple-600">
                    {username + " " + localStorage.getItem("Facility")}
                  </span>
                </li>
              )}
              <li className="hover:bg-gray-700">
                <button
                  onClick={handleLogout}
                  className="block w-full text-left px-4 py-2"
                >
                  Logout
                </button>
              </li>
            </ul>
          )}
        </div>
      </div>
    </div>
  );
};
