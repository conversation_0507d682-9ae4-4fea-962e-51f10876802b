import React, { useState, useEffect } from "react";
import {
    handleChange,
    handleObjectChange,
    handleArrayChange,
    addArrayItem,
} from "../../hooks/useFormHandlers";
import { defaultPatientRegistrationPayload } from "../../types/patient";
import type { PatientRegistrationPayload } from "../../types/patient";
import { patientSchema } from "../../zod_validations/patient/patientSchema";
import { ZodError } from "zod";
import { createPatient, getPatientById, updatePatient } from "../../services/patientApis";
import { showError, showSuccess } from "../../utils/toastUtils";
import { useNavigate } from "react-router-dom";
import { usePatientFormStore } from "../../store/patientFormStore";
import { AddressType } from "../../types/patientenums";

// Import the new section components
import { PatientVerificationSection } from "./sections/PatientVerificationSection";
import { PatientBasicInfoSection } from "./sections/PatientBasicInfoSection";
import { PatientContactAddressSection } from "./sections/PatientContactAddressSection";
import { PatientAdditionalInfoSection } from "./sections/PatientAdditionalInfoSection";



type Props = {
    patientId?: string;
};

export const PatientRegistrationForm: React.FC<Props> = ({ patientId }) => {
    const [form, setForm] = useState<PatientRegistrationPayload>(
        defaultPatientRegistrationPayload
    );

    const { quickFormData, clearQuickFormData } = usePatientFormStore();
    const [presentSameAsPermanent, setPresentSameAsPermanent] = React.useState(false);



    const onChange = handleChange(setForm);

    const onChangeWithIdentifierCheck = (
        e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
    ) => {
        const { name } = e.target;
        onChange(e);
        // If identifierNumber is typed before identifierType is selected, set an error
        if (name === "identifierNumber" && !form.identifierType) {
            setFormErrors((prev) => ({
                ...prev,
                identifierType: "Please select Identifier Type before entering Identifier Number",
            }));
        }
        // Clear error if identifierType is selected
        if (name === "identifierType") {
            setFormErrors((prev) => {
                const { identifierType: _, ...rest } = prev;
                return rest;
            });
        }
    };



    const onContactChange = (index: number) => handleArrayChange(setForm, "contacts", index);
    const onAddressChange = (index: number) => handleArrayChange(setForm, "addresses", index);
    const onEmergencyChange = (index: number) => handleArrayChange(setForm, "emergencyContacts", index);
    const onReferralChange = (index: number) => handleArrayChange(setForm, "referrals", index);
    const onObjectChange = (key: keyof PatientRegistrationPayload) => handleObjectChange(setForm, key);
    const navigate = useNavigate();
    const [formErrors, setFormErrors] = useState<Record<string, string>>({});    // const handleSubmit = (e: React.FormEvent) => {

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        try {
            patientSchema.parse(form); // Zod validation
            setFormErrors({}); // Clear previous errors
            const fullName = `${form.firstName} ${form.middleName || ""} ${form.lastName}`.trim();

            if (patientId) {
                const { success, error } = await updatePatient(patientId, form);
                if (success) {
                    showSuccess("Patient updated successfully", `Patient Name:${fullName}`);
                    navigate("/list")
                } else {
                    console.log(error)
                    showError("Update Failed", `Error updating patient`);
                }
            } else {
                const { success, error, data } = await createPatient(form);
                if (success) {
                    console.log("Created Patient:", data);
                    showSuccess("Patient Created Successfully", `Patient Name:${fullName}`);
                    navigate("/list")
                } else {
                    console.log(error)
                    showError("Creating Patient Failed", `Error in creating patient`);
                }
            }
        } catch (error: any) {
            if (error instanceof ZodError) {
                const fieldErrors: Record<string, string> = {};
                error.errors.forEach((e) => {
                    const path = e.path.join(".");
                    fieldErrors[path] = e.message;
                });
                setFormErrors(fieldErrors);
                showError("Fix errors in form", "Please correct the highlighted errors before submitting.");

            } else {
                console.error("Unexpected error:", error);
            }
        }
    };

    useEffect(() => {
        if (form.dateOfBirth) {
            const birthDate = new Date(form.dateOfBirth);
            const today = new Date();
            let age = today.getFullYear() - birthDate.getFullYear();
            const m = today.getMonth() - birthDate.getMonth();
            if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
                age--;
            }

            setForm(prev => ({
                ...prev,
                age
            }));
        }
    }, [form.dateOfBirth]);

    useEffect(() => {
        const fetchPatient = async () => {
            if (!patientId) return;

            try {
                const result = await getPatientById(patientId);
                if (Array.isArray(result) && result.length > 0) {
                    setForm(result[0]); // Take first object in array
                } else {
                    console.warn("No patient found for ID:", patientId);
                }
            } catch (error) {
                console.error("Error loading patient:", error);
            }
        };

        fetchPatient();
    }, [patientId]);

    const isEditMode = Boolean(patientId);
    const handleInformationSharingChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, checked } = e.target;
        setForm(prevForm => ({
            ...prevForm,
            informationSharing: {
                ...prevForm.informationSharing,
                [name]: checked
            }
        }));
    };

    useEffect(() => {
        if (!patientId && quickFormData && quickFormData.firstName) {
            setForm(quickFormData);
            clearQuickFormData(); // Optional: clear after loading
        }
    }, [patientId, quickFormData, clearQuickFormData]);


    const addAddress = () => {
        const hasPermanent = form.addresses?.some(a => a.addressType === "Permanent");
        const hasPresent = form.addresses?.some(a => a.addressType === "Present");

        if (form.addresses?.length >= 2) {
            showError("Limit reached", "Only one Permanent and one Present address allowed.");
            return;
        }

        if (!hasPermanent) {
            addArrayItem(setForm, "addresses", {
                addressType: "Permanent",
                houseNoOrFlatNo: null,
                localityOrSector: null,
                cityOrVillage: null,
                pincode: null,
                districtId: null,
                stateId: null,
                country: null,
            });
        } else if (!hasPresent) {
            addArrayItem(setForm, "addresses", {
                addressType: "Present",
                houseNoOrFlatNo: null,
                localityOrSector: null,
                cityOrVillage: null,
                pincode: null,
                districtId: null,
                stateId: null,
                country: null,
            });
        } else {
            showError("Both addresses added", "You already have Permanent and Present addresses.");
        }
    };

    const onAddressTypeChange = (index: number) => (e: React.ChangeEvent<HTMLSelectElement>) => {
        const newType = e.target.value;
        const hasPermanent = form.addresses.some((a, i) => a.addressType === "Permanent" && i !== index);
        const hasPresent = form.addresses.some((a, i) => a.addressType === "Present" && i !== index);

        if (newType === "Permanent" && hasPermanent) {
            showError("Duplicate Permanent", "Only one Permanent address is allowed.");
            return; // ignore change
        }
        if (newType === "Present" && hasPresent) {
            showError("Duplicate Present", "Only one Present address is allowed.");
            return; // ignore change
        }

        // Update addressType normally if no duplicates
        setForm(prev => {
            const addresses = [...(prev.addresses || [])];
            const typedAddressType = newType === AddressType.Permanent ? AddressType.Permanent : AddressType.Present;
            addresses[index] = { ...addresses[index], addressType: typedAddressType };

            return { ...prev, addresses };
        });
    };

    const onCheckboxChange = (index: number) => {
        const newVal = !presentSameAsPermanent;
        setPresentSameAsPermanent(newVal);

        if (newVal) {
            // Copy Permanent address to Present address at index
            const permanentAddress = form.addresses.find(a => a.addressType === "Permanent");
            if (permanentAddress) {
                setForm(prev => {
                    const addresses = [...(prev.addresses || [])];
                    addresses[index] = { ...permanentAddress, addressType: AddressType.Present };
                    return { ...prev, addresses };
                });
            }
        }
    };


    return (

        <form onSubmit={handleSubmit} className="space-y-10 px-6 py-2 bg-white shadow-xl rounded-xl">

            <div className="text-center mb-1">
                <h2 className="text-lg font-semibold">{isEditMode ? "Edit Patient" : "Patient Registration"}</h2>
            </div>

            {/* Patient Verification Section */}
            <PatientVerificationSection isEditMode={isEditMode} />

            {/* Basic Information Section */}
            <PatientBasicInfoSection
                form={form}
                formErrors={formErrors}
                isEditMode={isEditMode}
                onChange={onChange}
                onChangeWithIdentifierCheck={onChangeWithIdentifierCheck}
                onObjectChange={onObjectChange}
            />


            {/* Contact and Address Section */}
            <PatientContactAddressSection
                form={form}
                setForm={setForm}
                formErrors={formErrors}
                onContactChange={onContactChange}
                onAddressChange={onAddressChange}
                onAddressTypeChange={onAddressTypeChange}
                addAddress={addAddress}
                onCheckboxChange={onCheckboxChange}
                presentSameAsPermanent={presentSameAsPermanent}
            />





            {/* Additional Information Section */}
            <PatientAdditionalInfoSection
                form={form}
                setForm={setForm}
                formErrors={formErrors}
                isEditMode={isEditMode}
                onChange={onChange}
                onEmergencyChange={onEmergencyChange}
                onReferralChange={onReferralChange}
                onObjectChange={onObjectChange}
                handleInformationSharingChange={handleInformationSharingChange}
            />




            <div className="flex justify-center w-full max-w-5xl mx-auto gap-4 px-6">
                <button
                    type="submit"
                    className="basis-1/2 bg-indigo-600 text-white py-2 rounded-md font-medium hover:bg-indigo-700"
                >
                    {isEditMode ? "Update Patient" : "Create Patient"}
                </button>

                <button
                    type="button"
                    className="basis-1/2  bg-gray-300 text-gray-700 py-2 rounded-md font-medium hover:bg-gray-50"
                    onClick={(e) => {
                        e.preventDefault();
                        navigate("/list");
                    }}
                >
                    Cancel
                </button>
            </div>


        </form>
    );
};
