import React, { useState, useEffect } from "react";
import {
    handleChange,
    handleObjectChange,
    handleArrayChange,
    addArrayItem,
} from "../../hooks/useFormHandlers";
import { defaultPatientRegistrationPayload } from "../../types/patient";
import type { PatientRegistrationPayload } from "../../types/patient";
import { patientSchema } from "../../zod_validations/patient/patientSchema";
import { ZodError } from "zod";
import { createPatient, getPatientById, updatePatient } from "../../services/patientApis";
import { showError, showSuccess } from "../../utils/toastUtils";
import { useNavigate } from "react-router-dom";
import { usePatientFormStore } from "../../store/patientFormStore";
import { AddressType } from "../../types/patientenums";

// Import the new section components
import { PatientVerificationSection } from "./sections/PatientVerificationSection";
import { PatientBasicInfoSection } from "./sections/PatientBasicInfoSection";
import { PatientContactAddressSection } from "./sections/PatientContactAddressSection";
import { PatientAdditionalInfoSection } from "./sections/PatientAdditionalInfoSection";

type Props = {
    patientId?: string;
};

export const PatientRegistrationForm: React.FC<Props> = ({ patientId }) => {
    const [form, setForm] = useState<PatientRegistrationPayload>(
        defaultPatientRegistrationPayload
    );

    const { quickFormData, clearQuickFormData } = usePatientFormStore();
    const [presentSameAsPermanent, setPresentSameAsPermanent] = React.useState(false);

    const onChange = handleChange(setForm);

    const onChangeWithIdentifierCheck = (
        e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
    ) => {
        const { name } = e.target;
        onChange(e);
        // If identifierNumber is typed before identifierType is selected, set an error
        if (name === "identifierNumber" && !form.identifierType) {
            setFormErrors((prev) => ({
                ...prev,
                identifierType: "Please select Identifier Type before entering Identifier Number",
            }));
        }
        // Clear error if identifierType is selected
        if (name === "identifierType") {
            setFormErrors((prev) => {
                const { identifierType: _, ...rest } = prev;
                return rest;
            });
        }
    };

    const onContactChange = (index: number) => handleArrayChange(setForm, "contacts", index);
    const onAddressChange = (index: number) => handleArrayChange(setForm, "addresses", index);
    const onEmergencyChange = (index: number) => handleArrayChange(setForm, "emergencyContacts", index);
    const onReferralChange = (index: number) => handleArrayChange(setForm, "referrals", index);
    const onObjectChange = (key: keyof PatientRegistrationPayload) => handleObjectChange(setForm, key);
    const navigate = useNavigate();
    const [formErrors, setFormErrors] = useState<Record<string, string>>({}); 
    const facilityNameToIdMap: Record<string, number> = {
        "City Health Center": 1,
        "Green Valley Hospital": 2,
        "Civil Hospital Shillong": 3
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        // Step 1: Read facility name from localStorage
        const storedFacilityName = localStorage.getItem("Facility");
        if (!storedFacilityName) {
            showError("Missing Facility", "Facility not found in localStorage.");
            return;
        }

        // Step 2: Map facility name to numeric ID
        const facilityNameToIdMap: Record<string, number> = {
            "City Health Center": 1,
            "Green Valley Hospital": 2,
            "Civil Hospital Shillong": 3
        };

        const facilityIdNumber = facilityNameToIdMap[storedFacilityName];
        if (!facilityIdNumber) {
            showError("Invalid Facility", `No numeric ID found for: ${storedFacilityName}`);
            return;
        }

        // Step 3: Inject stringified numeric ID into the payload
        const formWithFacility: PatientRegistrationPayload = {
            ...form,
            facilityId: facilityIdNumber.toString(), // send as string
        };

        try {
            // Step 4: Validate using Zod
            patientSchema.parse(formWithFacility);
            setFormErrors({}); // Clear old errors

            const fullName = `${form.firstName} ${form.middleName || ""} ${form.lastName}`.trim();

            // Step 5: Create or update
            if (patientId) {
                const { success, error } = await updatePatient(patientId, formWithFacility);
                if (success) {
                    showSuccess("Patient updated successfully", `Patient Name: ${fullName}`);
                    navigate("/list");
                } else {
                    console.error("Update error:", error);
                    showError("Update Failed", "Error updating patient.");
                }
            } else {
                const { success, error, data } = await createPatient(formWithFacility);
                if (success) {
                    showSuccess("Patient Created Successfully", `Patient Name: ${fullName}`);
                    navigate("/list");
                } else {
                    console.error("Creation error:", error);

                    const status = error?.status;
                    const message = error?.message?.toLowerCase?.() || "";

                    if (status === 409 || message.includes("already exists")) {
                        showError("User Already Exists", "A patient with this identifier already exists.");
                    } else {
                        showError("Creating Patient Failed", "Error in creating patient.");
                    }
                }
            }

        } catch (error: any) {
            if (error instanceof ZodError) {
                const fieldErrors: Record<string, string> = {};
                error.errors.forEach((e) => {
                    const path = e.path.join(".");
                    fieldErrors[path] = e.message;
                });
                setFormErrors(fieldErrors);
                showError("Fix errors in form", "Please correct the highlighted errors before submitting.");
            } else {
                console.error("Unexpected error:", error);
                showError("Submission Failed", "An unexpected error occurred.");
            }
        }
    };

    useEffect(() => {
        if (form.dateOfBirth) {
            const birthDate = new Date(form.dateOfBirth);
            const today = new Date();
            let age = today.getFullYear() - birthDate.getFullYear();
            const m = today.getMonth() - birthDate.getMonth();
            if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
                age--;
            }

            setForm(prev => ({
                ...prev,
                age
            }));
        }
    }, [form.dateOfBirth]);

    useEffect(() => {
        const fetchPatient = async () => {
            if (!patientId) return;

            try {
                const result = await getPatientById(patientId);
                if (Array.isArray(result) && result.length > 0) {
                    setForm(result[0]); // Take first object in array
                } else {
                    console.warn("No patient found for ID:", patientId);
                }
            } catch (error) {
                console.error("Error loading patient:", error);
            }
        };

        fetchPatient();
    }, [patientId]);

    const isEditMode = Boolean(patientId);
    const handleInformationSharingChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, checked } = e.target;
        setForm(prevForm => ({
            ...prevForm,
            informationSharing: {
                ...prevForm.informationSharing,
                [name]: checked
            }
        }));
    };

    useEffect(() => {
        if (!patientId && quickFormData && quickFormData.firstName) {
            const storedFacilityName = localStorage.getItem("Facility");
            const facilityId = facilityNameToIdMap[storedFacilityName];
            if (!facilityId) {
                showError("Invalid Facility", `No numeric ID found for: ${storedFacilityName}`);
                return;
            }

            setForm({
                ...quickFormData,
                facilityId: facilityId.toString(),
            });
            clearQuickFormData(); // Optional: clear after loading
        }
    }, [patientId, quickFormData, clearQuickFormData]);

    const addAddress = () => {
        const hasPermanent = form.addresses?.some(a => a.addressType === "Permanent");
        const hasPresent = form.addresses?.some(a => a.addressType === "Present");

        if (form.addresses?.length >= 2) {
            showError("Limit reached", "Only one Permanent and one Present address allowed.");
            return;
        }

        if (!hasPermanent) {
            addArrayItem(setForm, "addresses", {
                addressType: "Permanent",
                houseNoOrFlatNo: null,
                localityOrSector: null,
                cityOrVillage: null,
                pincode: null,
                districtId: null,
                stateId: null,
                country: null,
            });
        } else if (!hasPresent) {
            addArrayItem(setForm, "addresses", {
                addressType: "Present",
                houseNoOrFlatNo: null,
                localityOrSector: null,
                cityOrVillage: null,
                pincode: null,
                districtId: null,
                stateId: null,
                country: null,
            });
        } else {
            showError("Both addresses added", "You already have Permanent and Present addresses.");
        }
    };

    const onAddressTypeChange = (index: number) => (e: React.ChangeEvent<HTMLSelectElement>) => {
        const newType = e.target.value;
        const hasPermanent = form.addresses.some((a, i) => a.addressType === "Permanent" && i !== index);
        const hasPresent = form.addresses.some((a, i) => a.addressType === "Present" && i !== index);

        if (newType === "Permanent" && hasPermanent) {
            showError("Duplicate Permanent", "Only one Permanent address is allowed.");
            return; // ignore change
        }
        if (newType === "Present" && hasPresent) {
            showError("Duplicate Present", "Only one Present address is allowed.");
            return; // ignore change
        }

        // Update addressType normally if no duplicates
        setForm(prev => {
            const addresses = [...(prev.addresses || [])];
            const typedAddressType = newType === AddressType.Permanent ? AddressType.Permanent : AddressType.Present;
            addresses[index] = { ...addresses[index], addressType: typedAddressType };

            return { ...prev, addresses };
        });
    };

    const onCheckboxChange = (index: number) => {
        const newVal = !presentSameAsPermanent;
        setPresentSameAsPermanent(newVal);

        if (newVal) {
            // Copy Permanent address to Present address at index
            const permanentAddress = form.addresses.find(a => a.addressType === "Permanent");
            if (permanentAddress) {
                setForm(prev => {
                    const addresses = [...(prev.addresses || [])];
                    addresses[index] = { ...permanentAddress, addressType: AddressType.Present };
                    return { ...prev, addresses };
                });
            }
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
            <div className="container mx-auto px-2 py-2 max-w-8xl">
                
                {/* Enhanced Header Section */}
                <div className="">
                    <div className="text-center">
                        <h1 className="text-4xl font-bold text-gray-900 mb-2 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                            {isEditMode ? "Edit Patient Information" : "Patient Registration"}
                        </h1>
                        <p className="text-gray-600 text-lg">
                            {isEditMode ? "Update patient details and medical information" : "Register a new patient with comprehensive medical details"}
                        </p>
                        <div className="mt-4 w-24 h-1 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full mx-auto"></div>
                    </div>
                </div>

                {/* Enhanced Form Container */}
                <form onSubmit={handleSubmit} className="space-y-8">
                    <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
                        
                        {/* Patient Verification Section */}
                        <div className="bg-gradient-to-r from-blue-50 to-purple-50 border-b border-gray-100">
                            <div className="p-8">
                                <div className="flex items-center space-x-3 mb-6">
                                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    <h2 className="text-2xl font-bold text-gray-900">Patient Verification</h2>
                                </div>
                                <PatientVerificationSection isEditMode={isEditMode} />
                            </div>
                        </div>

                        {/* Basic Information Section */}
                        <div className="border-b border-gray-100">
                            <div className="p-8">
                                <div className="flex items-center space-x-3 mb-6">
                                    <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
                                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                        </svg>
                                    </div>
                                    <h2 className="text-2xl font-bold text-gray-900">Basic Information</h2>
                                </div>
                                <PatientBasicInfoSection
                                    form={form}
                                    formErrors={formErrors}
                                    isEditMode={isEditMode}
                                    onChange={onChange}
                                    onChangeWithIdentifierCheck={onChangeWithIdentifierCheck}
                                    onObjectChange={onObjectChange}
                                />
                            </div>
                        </div>

                        {/* Contact and Address Section */}
                        <div className="border-b border-gray-100">
                            <div className="p-8">
                                <div className="flex items-center space-x-3 mb-6">
                                    <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center">
                                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                        </svg>
                                    </div>
                                    <h2 className="text-2xl font-bold text-gray-900">Contact & Address</h2>
                                </div>
                                <PatientContactAddressSection
                                    form={form}
                                    setForm={setForm}
                                    formErrors={formErrors}
                                    onContactChange={onContactChange}
                                    onAddressChange={onAddressChange}
                                    onAddressTypeChange={onAddressTypeChange}
                                    addAddress={addAddress}
                                    onCheckboxChange={onCheckboxChange}
                                    presentSameAsPermanent={presentSameAsPermanent}
                                />
                            </div>
                        </div>

                        {/* Additional Information Section */}
                        <div className="border-b border-gray-100">
                            <div className="p-8">
                                <div className="flex items-center space-x-3 mb-6">
                                    <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center">
                                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                    </div>
                                    <h2 className="text-2xl font-bold text-gray-900">Additional Information</h2>
                                </div>
                                <PatientAdditionalInfoSection
                                    form={form}
                                    setForm={setForm}
                                    formErrors={formErrors}
                                    isEditMode={isEditMode}
                                    onChange={onChange}
                                    onEmergencyChange={onEmergencyChange}
                                    onReferralChange={onReferralChange}
                                    onObjectChange={onObjectChange}
                                    handleInformationSharingChange={handleInformationSharingChange}
                                />
                            </div>
                        </div>

                        {/* Enhanced Action Buttons */}
                        <div className="bg-gray-50 px-8 py-8">
                            <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-2xl mx-auto">
                                <button
                                    type="submit"
                                    className="group relative flex-1 inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white transition-all duration-200 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl hover:from-blue-700 hover:to-purple-700 hover:scale-105 hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transform"
                                >
                                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                    {isEditMode ? "Update Patient Information" : "Create Patient Record"}
                                    <div className="absolute inset-0 bg-white opacity-20 rounded-xl blur-xl group-hover:opacity-30 transition-opacity"></div>
                                </button>

                                <button
                                    type="button"
                                    className="group relative flex-1 inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-gray-700 transition-all duration-200 bg-white border-2 border-gray-300 rounded-xl hover:bg-gray-50 hover:border-gray-400 hover:scale-105 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transform"
                                    onClick={(e) => {
                                        e.preventDefault();
                                        navigate("/list");
                                    }}
                                >
                                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                    Cancel & Return
                                </button>
                            </div>
                            
                            {/* Additional helpful text */}
                            <div className="mt-6 text-center">
                                <p className="text-sm text-gray-500">
                                    All fields marked with <span className="text-red-500">*</span> are required. 
                                    Please ensure all information is accurate before submitting.
                                </p>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    );
};