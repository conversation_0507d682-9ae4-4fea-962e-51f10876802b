import React from "react";
import { X } from "lucide-react";

type Props = {
  patient: any;
  onClose: () => void;
};


const FieldRow = ({ label, value }: { label: string; value?: string | number | boolean }) => (
  <div className="text-sm space-x-1 break-words">
    <span className="font-semibold text-gray-600">{label}:</span>
    <span className="text-gray-800">{value !== null && value !== undefined && value !== "" ? String(value) : "N/A"}</span>
  </div>
);

const Section = ({ title, children, color = "from-gray-50 to-gray-100", bar = "from-gray-400 to-gray-600" }: {
  title: string;
  children: React.ReactNode;
  color?: string;
  bar?: string;
}) => (
  <div className="mt-6 bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
    <div className={`bg-gradient-to-r ${color} px-6 py-4 border-b border-gray-200`}>
      <div className="flex items-center space-x-3">
        <div className={`w-2 h-8 bg-gradient-to-b ${bar} rounded-full`}></div>
        <h4 className="text-md font-semibold text-gray-800">{title}</h4>
      </div>
    </div>
    <div className="p-6 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">{children}</div>
  </div>
);

const PatientViewModal: React.FC<Props> = ({ patient, onClose }) => {
  if (!patient) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black/30 backdrop-blur-sm flex justify-center items-center px-4 py-8 overflow-y-auto">
      <div className="bg-white rounded-2xl shadow-2xl p-8 w-full max-w-6xl border border-gray-200 relative max-h-[95vh] overflow-y-auto">

        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-600 hover:text-red-500 transition-colors"
          aria-label="Close"
        >
          <X size={24} />
        </button>

        {/* Header */}
        <div className="mb-8 text-center">
          <h3 className="text-2xl font-bold text-gray-800">Patient Overview</h3>
          <p className="text-sm text-gray-500">Comprehensive details of the selected patient</p>
        </div>

        {/* Sections */}
        <Section title="Basic Information" color="from-indigo-50 to-purple-50" bar="from-indigo-500 to-purple-600">
          <FieldRow label="Full Name" value={patient.fullName} />
          <FieldRow label="Title" value={patient.title} />
          <FieldRow label="First Name" value={patient.firstName} />
          <FieldRow label="Middle Name" value={patient.middleName} />
          <FieldRow label="Last Name" value={patient.lastName} />
          <FieldRow label="Gender" value={patient.gender} />
          <FieldRow label="Date of Birth" value={patient.dateOfBirth} />
          <FieldRow label="Age" value={patient.age} />
          <FieldRow label="Marital Status" value={patient.maritalStatus} />
          <FieldRow label="Occupation" value={patient.occupation} />
          <FieldRow label="Education" value={patient.education} />
          <FieldRow label="Religion" value={patient.religion} />
          <FieldRow label="Caste" value={patient.caste} />
          <FieldRow label="Citizenship" value={patient.citizenship} />
          <FieldRow label="Annual Income" value={patient.annualIncome} />
          <FieldRow label="Blood Group" value={patient.bloodGroup} />
          <FieldRow label="Registration Date" value={patient.registrationDate} />
          <FieldRow label="Active" value={patient.isActive ? "Yes" : "No"} />
          <FieldRow label="Deceased" value={patient.isDeceased ? "Yes" : "No"} />
        </Section>

        <Section title="Identification" color="from-blue-50 to-sky-50" bar="from-blue-500 to-sky-600">
          <FieldRow label="Identifier Type" value={patient.identifierType} />
          <FieldRow label="Identifier Number" value={patient.identifierNumber} />
          <FieldRow label="Facility ID" value={patient.facilityId} />
        </Section>

        <Section title="Contacts" color="from-green-50 to-emerald-50" bar="from-green-500 to-emerald-600">
          {patient.contacts?.map((contact: any, idx: number) => {
            if (idx === 0) {
              // Index 0: show usual fields
              return (
                <React.Fragment key={idx}>
                  <FieldRow label={`Contact ${idx + 1} Phone`} value={contact.phoneNumber} />
                  <FieldRow label={`Contact ${idx + 1} Email`} value={contact.email} />
                  {/* <FieldRow label={`Contact ${idx + 1} Relationship`} value={contact.relationship} /> */}
                  <FieldRow label={`Phone Contact Preference ${idx + 1}`} value={contact.phoneContactPreference} />
                  {/* <FieldRow label={`Preferred Contact Mode ${idx + 1}`} value={contact.preferredContactMode} /> */}
                </React.Fragment>
              );
            } else {
              // Index >=1: show two alternate numbers, numbered continuously
              const alternateNumber1Label = `Alternate Number ${2 * idx - 1}`; // 1, 3, 5, ...
              const alternateNumber2Label = `Alternate Number ${2 * idx}`;     // 2, 4, 6, ...

              return (
                <React.Fragment key={idx}>
                  <FieldRow label={alternateNumber1Label} value={contact.phoneNumber} />
                  <FieldRow label={alternateNumber2Label} value={contact.mobileNumber} />
                </React.Fragment>
              );
            }
          })}
        </Section>



        <Section title="Emergency Contacts" color="from-pink-50 to-rose-50" bar="from-pink-500 to-rose-600">
          {patient.emergencyContacts?.map((ec: any, idx: number) => (
            <React.Fragment key={idx}>
              <FieldRow label="Name" value={ec.contactName} />
              <FieldRow label="Phone" value={ec.phoneNumber} />
              <FieldRow label="Relationship" value={ec.relationship} />
            </React.Fragment>
          ))}
        </Section>

        <Section title="Insurance Details" color="from-teal-50 to-cyan-50" bar="from-teal-500 to-cyan-600">
          <FieldRow label="Provider" value={patient.insurance?.insuranceProvider} />
          <FieldRow label="Policy Number" value={patient.insurance?.policyNumber} />
          <FieldRow label="Start Date" value={patient.insurance?.policyStartDate} />
          <FieldRow label="End Date" value={patient.insurance?.policyEndDate} />
          <FieldRow label="Coverage Amount" value={patient.insurance?.coverageAmount} />
        </Section>

        <Section title="Addresses" color="from-yellow-50 to-amber-50" bar="from-yellow-500 to-amber-600">
          {patient.addresses?.length > 0 ? (
            patient.addresses.map((addr: any, idx: number) => (
              <React.Fragment key={idx}>
                <FieldRow label={`Address ${idx + 1} - Type`} value={addr.addressType} />
                <FieldRow label={`Address ${idx + 1} - House / Flat No.`} value={addr.houseNoOrFlatNo} />
                <FieldRow label={`Address ${idx + 1} - Locality / Sector`} value={addr.localityOrSector} />
                <FieldRow label={`Address ${idx + 1} - City / Village`} value={addr.cityOrVillage} />
                <FieldRow label={`Address ${idx + 1} - District`} value={addr.districtId} />
                <FieldRow label={`Address ${idx + 1} - State`} value={addr.stateId} />
                <FieldRow label={`Address ${idx + 1} - Country`} value={addr.country} />
                <FieldRow label={`Address ${idx + 1} - Pincode`} value={addr.pincode} />
                {idx < patient.addresses.length - 1 && (
                  <div className="col-span-full border-t border-gray-200 my-2"></div>
                )}
              </React.Fragment>
            ))
          ) : (
            <FieldRow label="Addresses" value="None" />
          )}
        </Section>

        <Section title="Referrals & Billing" color="from-orange-50 to-yellow-50" bar="from-orange-500 to-yellow-600">
          <FieldRow label="Billing Type" value={patient.billingReferral?.billingType} />
          <FieldRow label="Referred By" value={patient.billingReferral?.referredBy} />
        </Section>

        <Section title="Information Sharing" color="from-fuchsia-50 to-purple-50" bar="from-fuchsia-500 to-purple-600">
          <FieldRow label="With Spouse" value={patient.informationSharing?.shareWithSpouse ? "Yes" : "No"} />
          <FieldRow label="With Children" value={patient.informationSharing?.shareWithChildren ? "Yes" : "No"} />
          <FieldRow label="With Caregiver" value={patient.informationSharing?.shareWithCaregiver ? "Yes" : "No"} />
          <FieldRow label="With Other" value={patient.informationSharing?.shareWithOther ? "Yes" : "No"} />
        </Section>

        {/* Mobile close button */}
        <div className="text-right mt-8 sm:hidden">
          <button className="bg-gray-100 hover:bg-gray-200 text-sm px-4 py-2 rounded shadow" onClick={onClose}>
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default PatientViewModal;
