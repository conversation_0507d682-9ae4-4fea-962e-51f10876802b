import React, { useState } from "react";
import { handleChange, handleArrayChange } from "../../hooks/useFormHandlers";
import { defaultPatientRegistrationPayload } from "../../types/patient";
import type { PatientRegistrationPayload } from "../../types/patient";
import { FormField } from "../../../src/commonfields/FormField";
import { Input } from "../../../src/commonfields/Input";
import { Select } from "../../../src/commonfields/Select";
import { Calendar } from "../../../src/commonfields/Calendar";
import { Button } from "../../../src/commonfields/Button";
import { Link } from "react-router-dom";
import {
  titleOptions,
  genderOptions,
  relationTypeOptions,
  identifierTypeOptions,
} from "../../types/patientenums";
import { createPatient } from "../../services/patientApis";
import { showError, showSuccess } from "../../utils/toastUtils";
import { usePatientFormStore } from "../../store/patientFormStore";
import FacilitySelector from "../../commonfields/FacilitySelector";
import { patientSchema } from "../../zod_validations/patient/patientSchema";
import { ZodError } from "zod";
import FormMessage from "../../commonfields/FormMessage";

type Props = {
  onClose: () => void;
  onSuccess?: () => void;
};

export const RegisterPatientDrawer: React.FC<Props> = ({ onClose, onSuccess }) => {
  const [form, setForm] = useState<PatientRegistrationPayload>(defaultPatientRegistrationPayload);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  const onBasicChange = handleChange(setForm);
  const onBasicChangeWithIdentifierCheck = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;

    // Update the form state
    onBasicChange(e);

    // Runtime check: if identifierNumber is typed first without type
    if (name === "identifierNumber" && !form.identifierType) {
      setFormErrors((prev) => ({
        ...prev,
        identifierType: "Please select Identifier Type before entering Identifier Number",
      }));
    }

    // Clear identifierType error once selected
    if (name === "identifierType") {
      setFormErrors((prev) => {
        const updated = { ...prev };
        delete updated["identifierType"];
        return updated;
      });
    }
  };

  const onContactChange = handleArrayChange(setForm, "contacts", 0);
  const onEmergencyChange = handleArrayChange(setForm, "emergencyContacts", 0);
  const { setQuickFormData } = usePatientFormStore();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      patientSchema.parse(form); // ✅ Run Zod validation
      setFormErrors({});

      const fullName = `${form.firstName} ${form.middleName || ""} ${form.lastName}`.trim();
      const { success, data, error } = await createPatient(form);

      if (success) {
        showSuccess("Quick Registration Successful", fullName);
        window.dispatchEvent(new Event("patient:registered"));
        if (onSuccess) onSuccess();
        onClose();
      } else {
        showError("Error Creating Patient", fullName);
        console.error("Error creating patient:", error);
      }

    } catch (error) {
      if (error instanceof ZodError) {
        const fieldErrors: Record<string, string> = {};
        error.errors.forEach((e) => {
          const path = e.path.join(".");
          fieldErrors[path] = e.message;
        });
        setFormErrors(fieldErrors);
        showError("Form Error", "Please fix the highlighted fields.");
      } else {
        console.error("Unexpected error during registration:", error);
      }
    }
  };

  return (
    <div className="fixed top-0 right-0 w-full max-w-md h-full bg-white shadow-lg z-50 p-6 overflow-y-auto">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">Quick Register Patient</h2>
        <button onClick={onClose} className="text-xl font-bold">&times;</button>
      </div>

      <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField label="Title" required>
          <Select name="title" value={form.title || ""} onChange={onBasicChange}>
            <option value="">Select</option>
            {titleOptions.map((t) => (
              <option key={t} value={t}>{t}</option>
            ))}
          </Select>
          <FormMessage>{formErrors["title"]}</FormMessage>
        </FormField>

        <FormField label="First Name" required>
          <Input name="firstName" value={form.firstName || ""} onChange={onBasicChange} />
          <FormMessage>{formErrors["firstName"]}</FormMessage>
        </FormField>

        <FormField label="Middle Name">
          <Input name="middleName" value={form.middleName || ""} onChange={onBasicChange} />
          <FormMessage>{formErrors["middleName"]}</FormMessage>
        </FormField>

        <FormField label="Last Name" required>
          <Input name="lastName" value={form.lastName || ""} onChange={onBasicChange} />
          <FormMessage>{formErrors["lastName"]}</FormMessage>
        </FormField>

        <FormField label="Date of Birth" required>
          <Calendar name="dateOfBirth" value={form.dateOfBirth || ""} onChange={onBasicChange} />
          <FormMessage>{formErrors["dateOfBirth"]}</FormMessage>
        </FormField>

        <FormField label="Gender" required>
          <Select name="gender" value={form.gender || ""} onChange={onBasicChange}>
            <option value="">Select</option>
            {genderOptions.map((g) => (
              <option key={g} value={g}>{g}</option>
            ))}
          </Select>
          <FormMessage>{formErrors["gender"]}</FormMessage>
        </FormField>

        <FormField label="Phone Number" required>
          <Input
            name="phoneNumber"
            value={form.contacts?.[0]?.phoneNumber || ""}
            onChange={onContactChange}
          />
          <FormMessage>{formErrors["contacts.0.phoneNumber"]}</FormMessage>
        </FormField>

        <FormField label="Emergency Contact Name" required>
          <Input
            name="contactName"
            value={form.emergencyContacts?.[0]?.contactName || ""}
            onChange={onEmergencyChange}
          />
          <FormMessage>{formErrors["emergencyContacts.0.contactName"]}</FormMessage>
        </FormField>

        <FormField label="Relationship" required>
          <Select
            name="relationship"
            value={form.emergencyContacts?.[0]?.relationship || ""}
            onChange={onEmergencyChange}
          >
            <option value="">Select</option>
            {relationTypeOptions.map((r) => (
              <option key={r} value={r}>{r}</option>
            ))}
          </Select>
          <FormMessage>{formErrors["emergencyContacts.0.relationship"]}</FormMessage>
        </FormField>

        <FormField label="Emergency Phone Number" required>
          <Input
            name="phoneNumber"
            value={form.emergencyContacts?.[0]?.phoneNumber || ""}
            onChange={onEmergencyChange}
          />
          <FormMessage>{formErrors["emergencyContacts.0.phoneNumber"]}</FormMessage>
        </FormField>

        <FormField label="Identifier Type" required>
          <Select
            name="identifierType"
            value={form.identifierType || ""}
            onChange={onBasicChangeWithIdentifierCheck}
          >
            <option value="">Select</option>
            {identifierTypeOptions.map((type) => (
              <option key={type} value={type}>{type}</option>
            ))}
          </Select>
          <FormMessage>{formErrors["identifierType"]}</FormMessage>
        </FormField>

        <FormField label="Identifier Number" required>
          <Input
            name="identifierNumber"
            value={form.identifierNumber || ""}
            onChange={onBasicChangeWithIdentifierCheck}
            placeholder="Enter identifier value"
          />
          <FormMessage>{formErrors["identifierNumber"]}</FormMessage>
        </FormField>


        <FormField label="Facility" required>
          <FacilitySelector
            name="facilityId"
            value={form.facilityId || ""}
            onChange={(value) => onBasicChange({ target: { name: "facilityId", value } } as React.ChangeEvent<HTMLInputElement>)}
          />
          <FormMessage>{formErrors["facilityId"]}</FormMessage>
        </FormField>

        <div className="col-span-2 mt-2">
          <Link
            to="/patients"
            className="text-sm text-blue-600 hover:underline"
            onClick={() => { setQuickFormData(form); onClose(); }}
          >
            + Add more details
          </Link>
        </div>

        {/* Footer Buttons */}
        <div className="flex gap-4 mt-6">
          <Button type="submit" variant="primary">Register Patient</Button>
          <Button type="button" variant="outline" onClick={onClose}>Cancel</Button>
        </div>
      </form>
    </div>
  );
};
