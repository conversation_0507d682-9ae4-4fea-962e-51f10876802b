import React from "react";
import { FormField } from "../../../commonfields/FormField";
import { Input } from "../../../commonfields/Input";
import { Select } from "../../../commonfields/Select";
import { Calendar } from "../../../commonfields/Calendar";
import FormMessage from "../../../commonfields/FormMessage";
import FacilitySelector from "../../../commonfields/FacilitySelector";
import {
    relationTypeOptions,
    billingTypeOptions,
    citizenshipOptions,
    religionOptions,
    casteOptions,
    occupationOptions,
    educationOptions,
    annualIncomeOptions,
} from "../../../types/patientenums";
import type { PatientRegistrationPayload } from "../../../types/patient";
import { addArrayItem, removeArrayItem } from "../../../hooks/useFormHandlers";

type Props = {
    form: PatientRegistrationPayload;
    setForm: React.Dispatch<React.SetStateAction<PatientRegistrationPayload>>;
    formErrors: Record<string, string>;
    isEditMode: boolean;
    onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    onEmergencyChange: (index: number) => (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    onReferralChange: (index: number) => (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    onObjectChange: (key: keyof PatientRegistrationPayload) => (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    onObjectChangeWithValidation: (key: keyof PatientRegistrationPayload) => (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    handleInformationSharingChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
};

export const PatientAdditionalInfoSection: React.FC<Props> = ({
    form,
    setForm,
    formErrors,
    isEditMode,
    onChange,
    onEmergencyChange,
    onReferralChange,
    onObjectChange,
     onObjectChangeWithValidation,
    handleInformationSharingChange,
}) => {
    return (
        <div className="space-y-8">
            {/* Emergency Contacts */}
            <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
                    <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-3">
                            <div className="w-2 h-8 bg-gradient-to-b from-blue-500 to-indigo-600 rounded-full"></div>
                            <h3 className="text-lg font-semibold text-gray-800">Emergency Contacts</h3>
                        </div>
                        <button
                            type="button"
                            className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg hover:from-blue-600 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transform transition-all duration-200 hover:scale-105 shadow-md"
                            onClick={() => addArrayItem(setForm, "emergencyContacts", {})}
                        >
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            Add Contact
                        </button>
                    </div>
                </div>
                <div className="p-6 space-y-4">
                    {form.emergencyContacts?.length > 0 ? (
                        form.emergencyContacts.map((contact, index) => (
                            <div key={index} className="bg-gray-50 rounded-lg p-6 border border-gray-200 hover:shadow-md transition-shadow duration-200">
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <FormField label="Contact Name">
                                        <Input 
                                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200" 
                                            name="contactName" 
                                            value={contact.contactName || ""} 
                                            onChange={onEmergencyChange(index)} 
                                        />
                                        <FormMessage>{formErrors?.[`emergencyContacts.${index}.contactName`]}</FormMessage>
                                    </FormField>
                                    <FormField label="Relationship">
                                        <Select 
                                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200" 
                                            name="relationship" 
                                            value={contact.relationship || ""} 
                                            onChange={onEmergencyChange(index)}
                                        >
                                            <option value="">Select Relationship</option>
                                            {relationTypeOptions.map(rel => (
                                                <option key={rel} value={rel}>{rel}</option>
                                            ))}
                                        </Select>
                                        <FormMessage>{formErrors?.[`emergencyContacts.${index}.relationship`]}</FormMessage>
                                    </FormField>
                                    <FormField label="Phone Number">
                                        <Input 
                                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200" 
                                            name="phoneNumber" 
                                            value={contact.phoneNumber || ""} 
                                            onChange={onEmergencyChange(index)} 
                                        />
                                        <FormMessage>{formErrors?.[`emergencyContacts.${index}.phoneNumber`]}</FormMessage>
                                    </FormField>
                                </div>
                                <div className="flex justify-end mt-4">
                                    <button
                                        type="button"
                                        className="inline-flex items-center px-4 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 hover:border-red-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200"
                                        onClick={() => removeArrayItem(setForm, "emergencyContacts", index)}
                                    >
                                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                        Remove
                                    </button>
                                </div>
                            </div>
                        ))
                    ) : (
                        <div className="text-center py-8 text-gray-500">
                            <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            <p className="text-sm">No emergency contacts added yet</p>
                        </div>
                    )}
                </div>
            </div>

            {/* Information Sharing Consent */}
            <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4 border-b border-gray-200">
                    <div className="flex items-center space-x-3">
                        <div className="w-2 h-8 bg-gradient-to-b from-green-500 to-emerald-600 rounded-full"></div>
                        <h3 className="text-lg font-semibold text-gray-800">Information Sharing Consent</h3>
                    </div>
                </div>
                <div className="p-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        {[
                            { label: "Share With Spouse", name: "shareWithSpouse" },
                            { label: "Share With Children", name: "shareWithChildren" },
                            { label: "Share With Caregiver", name: "shareWithCaregiver" },
                            { label: "Share With Other", name: "shareWithOther" }
                        ].map(({ label, name }) => (
                            <label key={name} className="group relative flex items-center p-4 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 hover:border-gray-300 cursor-pointer transition-all duration-200">
                                <input
                                    type="checkbox"
                                    name={name}
                                    checked={form.informationSharing?.[name] || false}
                                    onChange={handleInformationSharingChange}
                                    className="w-5 h-5 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500 focus:ring-2 transition-all duration-200"
                                />
                                <span className="ml-3 text-sm font-medium text-gray-700 group-hover:text-gray-900 transition-colors duration-200">{label}</span>
                            </label>
                        ))}
                    </div>
                </div>
            </div>

            {/* Additional Information */}
            <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
                <div className="bg-gradient-to-r from-purple-50 to-violet-50 px-6 py-4 border-b border-gray-200">
                    <div className="flex items-center space-x-3">
                        <div className="w-2 h-8 bg-gradient-to-b from-purple-500 to-violet-600 rounded-full"></div>
                        <h3 className="text-lg font-semibold text-gray-800">Additional Information</h3>
                    </div>
                </div>
                <div className="p-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <FormField label="Citizenship (optional)">
                            <Select className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200" name="citizenship" value={form.citizenship || ""} onChange={onChange}>
                                <option value="">Select Citizenship</option>
                                {citizenshipOptions.map((value) => (
                                    <option key={value} value={value}>{value}</option>
                                ))}
                            </Select>
                        </FormField>

                        <FormField label="Religion (optional)">
                            <Select className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200" name="religion" value={form.religion || ""} onChange={onChange}>
                                <option value="">Select Religion</option>
                                {religionOptions.map((value) => (
                                    <option key={value} value={value}>{value}</option>
                                ))}
                            </Select>
                        </FormField>

                        <FormField label="Caste (optional)">
                            <Select className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200" name="caste" value={form.caste || ""} onChange={onChange}>
                                <option value="">Select Caste</option>
                                {casteOptions.map((value) => (
                                    <option key={value} value={value}>{value}</option>
                                ))}
                            </Select>
                        </FormField>

                        <FormField label="Occupation (optional)">
                            <Select className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200" name="occupation" value={form.occupation || ""} onChange={onChange}>
                                <option value="">Select Occupation</option>
                                {occupationOptions.map((value) => (
                                    <option key={value} value={value}>{value}</option>
                                ))}
                            </Select>
                        </FormField>

                        <FormField label="Education (optional)">
                            <Select className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200" name="education" value={form.education || ""} onChange={onChange}>
                                <option value="">Select Education</option>
                                {educationOptions.map((value) => (
                                    <option key={value} value={value}>{value}</option>
                                ))}
                            </Select>
                        </FormField>

                        <FormField label="Annual Income (optional)">
                            <Select className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200" name="annualIncome" value={form.annualIncome || ""} onChange={onChange}>
                                <option value="">Select Annual Income</option>
                                {annualIncomeOptions.map((value) => (
                                    <option key={value} value={value}>{value}</option>
                                ))}
                            </Select>
                        </FormField>
                    </div>
                </div>
            </div>

            {/* Billing Referral */}
            <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
                <div className="bg-gradient-to-r from-orange-50 to-amber-50 px-6 py-4 border-b border-gray-200">
                    <div className="flex items-center space-x-3">
                        <div className="w-2 h-8 bg-gradient-to-b from-orange-500 to-amber-600 rounded-full"></div>
                        <h3 className="text-lg font-semibold text-gray-800">Billing Referral</h3>
                    </div>
                </div>
                <div className="p-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField label="Billing Type">
                            <Select
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                                name="billingType"
                                value={form.billingReferral?.billingType || ""}
                                onChange={onObjectChange("billingReferral")}
                            >
                                <option value="">Select Billing Type</option>
                                {billingTypeOptions.map((type) => (
                                    <option key={type} value={type}>{type}</option>
                                ))}
                            </Select>
                        </FormField>

                        <FormField label="Referred By">
                            <Input
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                                name="referredBy"
                                value={form.billingReferral?.referredBy || ""}
                                onChange={onObjectChange("billingReferral")}
                            />
                        </FormField>
                    </div>
                </div>
            </div>

            {/* Referrals */}
            {!isEditMode && (
                <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
                    <div className="bg-gradient-to-r from-teal-50 to-cyan-50 px-6 py-4 border-b border-gray-200">
                        <div className="flex justify-between items-center">
                            <div className="flex items-center space-x-3">
                                <div className="w-2 h-8 bg-gradient-to-b from-teal-500 to-cyan-600 rounded-full"></div>
                                <h3 className="text-lg font-semibold text-gray-800">Referrals</h3>
                            </div>
                            <button
                                type="button"
                                className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-teal-500 to-cyan-600 rounded-lg hover:from-teal-600 hover:to-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transform transition-all duration-200 hover:scale-105 shadow-md"
                                onClick={() => addArrayItem(setForm, "referrals", {})}
                            >
                                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                Add Referral
                            </button>
                        </div>
                    </div>
                    <div className="p-6 space-y-4">
                        {form.referrals?.length > 0 ? (
                            form.referrals.map((referral, index) => (
                                <div key={index} className="bg-gray-50 rounded-lg p-6 border border-gray-200 hover:shadow-md transition-shadow duration-200">
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                        <FormField label="From Facility ID">
                                            <FacilitySelector
                                                name="fromFacilityId"
                                                value={referral.fromFacilityId || ""}
                                                onChange={onReferralChange(index)}
                                            />
                                        </FormField>
                                        <FormField label="To Facility ID">
                                            <FacilitySelector
                                                name="toFacilityId"
                                                value={referral.toFacilityId || ""}
                                                onChange={onReferralChange(index)}
                                            />
                                        </FormField>
                                        <FormField label="Referral Date">
                                            <Calendar 
                                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200" 
                                                name="referralDate" 
                                                value={referral.referralDate || ""} 
                                                onChange={onReferralChange(index)} 
                                            />
                                        </FormField>
                                        <FormField label="Reason">
                                            <Input 
                                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200" 
                                                name="reason" 
                                                value={referral.reason || ""} 
                                                onChange={onReferralChange(index)} 
                                            />
                                        </FormField>
                                    </div>
                                    <div className="flex justify-end mt-4">
                                        <button
                                            type="button"
                                            className="inline-flex items-center px-4 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 hover:border-red-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200"
                                            onClick={() => removeArrayItem(setForm, "referrals", index)}
                                        >
                                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                            Remove
                                        </button>
                                    </div>
                                </div>
                            ))
                        ) : (
                            <div className="text-center py-8 text-gray-500">
                                <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <p className="text-sm">No referrals added yet</p>
                            </div>
                        )}
                    </div>
                </div>
            )}

            {/* Insurance */}
            <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
                <div className="bg-gradient-to-r from-indigo-50 to-purple-50 px-6 py-4 border-b border-gray-200">
                    <div className="flex items-center space-x-3">
                        <div className="w-2 h-8 bg-gradient-to-b from-indigo-500 to-purple-600 rounded-full"></div>
                        <h3 className="text-lg font-semibold text-gray-800">Insurance Information</h3>
                    </div>
                </div>
                <div className="p-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <FormField label="Insurance Provider">
                            <Input
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                                name="insuranceProvider"
                                value={form.insurance?.insuranceProvider || ""}
                                onChange={onObjectChange("insurance")}
                            />
                        </FormField>
                        <FormField label="Policy Number">
                            <Input
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                                name="policyNumber"
                                value={form.insurance?.policyNumber || ""}
                                onChange={onObjectChange("insurance")}
                            />
                        </FormField>
                        <FormField label="Coverage Amount">
                            <Input
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                                name="coverageAmount"
                                type="number"
                                value={form.insurance?.coverageAmount ?? ""}
                                onChange={onObjectChange("insurance")}
                            />
                        </FormField>
                        <FormField label="Policy Start Date">
                        <Calendar
                            name="policyStartDate"
                            value={form.insurance?.policyStartDate || ""}
                            onChange={onObjectChangeWithValidation("insurance")}
                            className="w-full px-3 py-2 text-sm rounded-md border border-gray-300 
                                      bg-white text-gray-900 "
                        />
                        <FormMessage>{formErrors?.["insurance.policyStartDate"]}</FormMessage>
                    </FormField>
                        <FormField label="Policy End Date">
                        <Calendar
                            name="policyEndDate"
                            value={form.insurance?.policyEndDate || ""}
                            onChange={onObjectChangeWithValidation("insurance")}
                            className="w-full px-3 py-2 text-sm rounded-md border border-gray-300 
                                      bg-white text-gray-900 "
                        />
                        <FormMessage>{formErrors?.["insurance.policyEndDate"]}</FormMessage>
                    </FormField>
                    </div>
                </div>
            </div>
        </div>
    );
};