import React from "react";
import { FormField } from "../../../commonfields/FormField";
import { Input } from "../../../commonfields/Input";
import { Select } from "../../../commonfields/Select";
import { Calendar } from "../../../commonfields/Calendar";
import FormMessage from "../../../commonfields/FormMessage";
import FacilitySelector from "../../../commonfields/FacilitySelector";
import {
    relationTypeOptions,
    billingTypeOptions,
    citizenshipOptions,
    religionOptions,
    casteOptions,
    occupationOptions,
    educationOptions,
    annualIncomeOptions,
} from "../../../types/patientenums";
import type { PatientRegistrationPayload } from "../../../types/patient";
import { addArrayItem, removeArrayItem } from "../../../hooks/useFormHandlers";

type Props = {
    form: PatientRegistrationPayload;
    setForm: React.Dispatch<React.SetStateAction<PatientRegistrationPayload>>;
    formErrors: Record<string, string>;
    isEditMode: boolean;
    onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    onEmergencyChange: (index: number) => (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    onReferralChange: (index: number) => (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    onObjectChange: (key: keyof PatientRegistrationPayload) => (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    handleInformationSharingChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
};

export const PatientAdditionalInfoSection: React.FC<Props> = ({
    form,
    setForm,
    formErrors,
    isEditMode,
    onChange,
    onEmergencyChange,
    onReferralChange,
    onObjectChange,
    handleInformationSharingChange,
}) => {
    return (
        <>
            {/* Emergency Contacts */}
            <div>
                <div className="mb-3 border border-gray-200 rounded-md shadow-sm p-3 bg-gray-50">
                    <div className="flex justify-between items-center mb-2">
                        <h3 className="text-base font-medium">Emergency Contacts</h3>
                        <button
                            type="button"
                            className="text-xs text-indigo-600 font-medium border border-indigo-300 px-2 py-1 rounded hover:bg-indigo-50 transition"
                            onClick={() => addArrayItem(setForm, "emergencyContacts", {})}
                        >
                            + Add
                        </button>
                    </div>
                    {form.emergencyContacts?.map((contact, index) => (
                        <div key={index} className="grid grid-cols-1 md:grid-cols-5 gap-3 p-3 mb-2 bg-white rounded-md">
                            <FormField label="Contact Name">
                                <Input className="text-sm py-1 px-2" name="contactName" value={contact.contactName || ""} onChange={onEmergencyChange(index)} />
                                <FormMessage>{formErrors?.[`emergencyContacts.${index}.contactName`]}</FormMessage>
                            </FormField>
                            <FormField label="Relationship">
                                <Select className="text-sm py-1 px-2" name="relationship" value={contact.relationship || ""} onChange={onEmergencyChange(index)}>
                                    <option value="">Select</option>
                                    {relationTypeOptions.map(rel => (
                                        <option key={rel} value={rel}>{rel}</option>
                                    ))}
                                </Select>
                                <FormMessage>{formErrors?.[`emergencyContacts.${index}.relationship`]}</FormMessage>
                            </FormField>
                            <FormField label="Phone Number">
                                <Input className="text-sm py-1 px-2" name="phoneNumber" value={contact.phoneNumber || ""} onChange={onEmergencyChange(index)} />
                                <FormMessage>{formErrors?.[`emergencyContacts.${index}.phoneNumber`]}</FormMessage>
                            </FormField>
                            <div className="w-full flex justify-end mt-1 md:col-span-5">
                                <button
                                    type="button"
                                    className="text-xs text-red-600 font-medium border border-red-300 px-3 py-1 rounded hover:bg-red-50 transition"
                                    onClick={() => removeArrayItem(setForm, "emergencyContacts", index)}
                                >
                                    Remove
                                </button>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Information Sharing Consent */}
            <div className="mb-3 border border-gray-200 rounded-md shadow-sm p-3 bg-gray-50">
                <h3 className="text-base font-medium mb-4">Information Sharing Consent</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {[
                        { label: "Share With Spouse", name: "shareWithSpouse" },
                        { label: "Share With Children", name: "shareWithChildren" },
                        { label: "Share With Caregiver", name: "shareWithCaregiver" },
                        { label: "Share With Other", name: "shareWithOther" }
                    ].map(({ label, name }) => (
                        <label key={name} className="flex items-center space-x-2 p-2 bg-white rounded border border-gray-200 shadow-sm hover:shadow-md transition">
                            <input
                                type="checkbox"
                                name={name}
                                checked={form.informationSharing?.[name] || false}
                                onChange={handleInformationSharingChange}
                                className="form-checkbox h-5 w-5 text-indigo-600"
                            />
                            <span className="text-sm text-gray-700 font-medium">{label}</span>
                        </label>
                    ))}
                </div>
            </div>

            {/* Additional Information */}
            <div className="mb-3 border border-gray-200 rounded-md shadow-sm p-3 bg-gray-50">
                <h3 className="text-base font-medium mb-2">Additional Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    <FormField label="Citizenship (optional)">
                        <Select className="text-sm py-1 px-2" name="citizenship" value={form.citizenship || ""} onChange={onChange}>
                            <option value="">Select</option>
                            {citizenshipOptions.map((value) => (
                                <option key={value} value={value}>{value}</option>
                            ))}
                        </Select>
                    </FormField>

                    <FormField label="Religion (optional)">
                        <Select className="text-sm py-1 px-2" name="religion" value={form.religion || ""} onChange={onChange}>
                            <option value="">Select</option>
                            {religionOptions.map((value) => (
                                <option key={value} value={value}>{value}</option>
                            ))}
                        </Select>
                    </FormField>

                    <FormField label="Caste (optional)">
                        <Select className="text-sm py-1 px-2" name="caste" value={form.caste || ""} onChange={onChange}>
                            <option value="">Select</option>
                            {casteOptions.map((value) => (
                                <option key={value} value={value}>{value}</option>
                            ))}
                        </Select>
                    </FormField>

                    <FormField label="Occupation (optional)">
                        <Select className="text-sm py-1 px-2" name="occupation" value={form.occupation || ""} onChange={onChange}>
                            <option value="">Select</option>
                            {occupationOptions.map((value) => (
                                <option key={value} value={value}>{value}</option>
                            ))}
                        </Select>
                    </FormField>

                    <FormField label="Education (optional)">
                        <Select className="text-sm py-1 px-2" name="education" value={form.education || ""} onChange={onChange}>
                            <option value="">Select</option>
                            {educationOptions.map((value) => (
                                <option key={value} value={value}>{value}</option>
                            ))}
                        </Select>
                    </FormField>

                    <FormField label="Annual Income (optional)">
                        <Select className="text-sm py-1 px-2" name="annualIncome" value={form.annualIncome || ""} onChange={onChange}>
                            <option value="">Select</option>
                            {annualIncomeOptions.map((value) => (
                                <option key={value} value={value}>{value}</option>
                            ))}
                        </Select>
                    </FormField>
                </div>
            </div>

            {/* Billing Referral */}
            <div className="mb-3 border border-gray-200 rounded-md shadow-sm p-3 bg-gray-50">
                <h3 className="text-base font-medium mb-2">Billing Referral</h3>
                <div className="grid grid-cols-1 md:grid-cols-5 gap-3">
                    <FormField label="Billing Type">
                        <Select
                            className="text-sm py-1 px-2"
                            name="billingType"
                            value={form.billingReferral?.billingType || ""}
                            onChange={onObjectChange("billingReferral")}
                        >
                            <option value="">Select</option>
                            {billingTypeOptions.map((type) => (
                                <option key={type} value={type}>{type}</option>
                            ))}
                        </Select>
                    </FormField>

                    <FormField label="Referred By">
                        <Input
                            className="text-sm py-1 px-2"
                            name="referredBy"
                            value={form.billingReferral?.referredBy || ""}
                            onChange={onObjectChange("billingReferral")}
                        />
                    </FormField>
                </div>
            </div>

            {/* Referrals */}
            {!isEditMode && (
                <div className="mb-3 rounded-md shadow-sm p-3">
                    <div className="flex justify-between items-center ">
                        <h3 className="text-base font-medium">Referrals</h3>
                        <button
                            type="button"
                            className="text-xs text-indigo-600 font-medium border-indigo-300 px-2 py-1 rounded hover:bg-indigo-50 transition"
                            onClick={() => addArrayItem(setForm, "referrals", {})}
                        >
                            + Add
                        </button>
                    </div>
                    {form.referrals?.map((referral, index) => (
                        <div key={index} className="grid grid-cols-1 md:grid-cols-5 gap-3 p-3 mb-2 bg-white rounded-md">
                            <FormField label="From Facility ID">
                                <FacilitySelector
                                    name="fromFacilityId"
                                    value={referral.fromFacilityId || ""}
                                    onChange={(value) => onReferralChange(index)({ target: { name: "fromFacilityId", value } } as React.ChangeEvent<HTMLInputElement>)}
                                />
                            </FormField>
                            <FormField label="To Facility ID">
                                <FacilitySelector
                                    name="toFacilityId"
                                    value={referral.toFacilityId || ""}
                                    onChange={(value) => onReferralChange(index)({ target: { name: "toFacilityId", value } } as React.ChangeEvent<HTMLInputElement>)}
                                />
                            </FormField>
                            <FormField label="Referral Date">
                                <Calendar className="text-sm py-1 px-2" name="referralDate" value={referral.referralDate || ""} onChange={onReferralChange(index)} />
                            </FormField>
                            <FormField label="Reason">
                                <Input className="text-sm py-1 px-2" name="reason" value={referral.reason || ""} onChange={onReferralChange(index)} />
                            </FormField>
                            <div className="w-full flex justify-end mt-1 md:col-span-5">
                                <button
                                    type="button"
                                    className="text-xs text-red-600 font-medium border border-red-300 px-3 py-1 rounded hover:bg-red-50 transition"
                                    onClick={() => removeArrayItem(setForm, "referrals", index)}
                                >
                                    Remove
                                </button>
                            </div>
                        </div>
                    ))}
                </div>
            )}

            {/* Insurance */}
            <div className="mb-3 border border-gray-200 rounded-md shadow-sm p-3 bg-gray-50">
                <h3 className="text-base font-medium mb-2">Insurance</h3>
                <div className="grid grid-cols-1 md:grid-cols-5 gap-3">
                    <FormField label="Insurance Provider">
                        <Input
                            className="text-sm py-1 px-2"
                            name="insuranceProvider"
                            value={form.insurance?.insuranceProvider || ""}
                            onChange={onObjectChange("insurance")}
                        />
                    </FormField>
                    <FormField label="Policy Number">
                        <Input
                            className="text-sm py-1 px-2"
                            name="policyNumber"
                            value={form.insurance?.policyNumber || ""}
                            onChange={onObjectChange("insurance")}
                        />
                    </FormField>
                    <FormField label="Policy Start Date">
                        <Calendar
                            className="text-sm py-1 px-2"
                            name="policyStartDate"
                            value={form.insurance?.policyStartDate || ""}
                            onChange={onObjectChange("insurance")}
                        />
                    </FormField>
                    <FormField label="Policy End Date">
                        <Calendar
                            className="text-sm py-1 px-2"
                            name="policyEndDate"
                            value={form.insurance?.policyEndDate || ""}
                            onChange={onObjectChange("insurance")}
                        />
                    </FormField>
                    <FormField label="Coverage Amount">
                        <Input
                            className="text-sm py-1 px-2"
                            name="coverageAmount"
                            type="number"
                            value={form.insurance?.coverageAmount ?? ""}
                            onChange={onObjectChange("insurance")}
                        />
                    </FormField>
                </div>
            </div>
        </>
    );
};
