import React from "react";
import { FormField } from "../../../commonfields/FormField";
import { Input } from "../../../commonfields/Input";
import { Select } from "../../../commonfields/Select";
import { Calendar } from "../../../commonfields/Calendar";
import FormMessage from "../../../commonfields/FormMessage";
import FacilitySelector from "../../../commonfields/FacilitySelector";
import {
    titleOptions,
    genderOptions,
    bloodGroupOptions,
    maritalStatusOptions,
    identifierTypeOptions,
} from "../../../types/patientenums";
import type { PatientRegistrationPayload } from "../../../types/patient";

type Props = {
    form: PatientRegistrationPayload;
    formErrors: Record<string, string>;
    isEditMode: boolean;
    onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    onChangeWithIdentifierCheck: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    onObjectChange: (key: keyof PatientRegistrationPayload) => (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
};

export const PatientBasicInfoSection: React.FC<Props> = ({
    form,
    formErrors,
    isEditMode,
    onChange,
    onChangeWithIdentifierCheck,
    onObjectChange,
}) => {
    return (
        <>
            {/* ABHA Details */}
            <div className="mb-3 bg-gray-50 rounded-md shadow-sm p-3">
                <h3 className="text-base font-medium mb-3">ABHA Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <FormField label="ABHA Number">
                        <Input
                            className="text-sm py-1 px-2"
                            name="abhaNumber"
                            value={form.abha?.abhaNumber || ""}
                            onChange={onObjectChange("abha")}
                        />
                        <FormMessage>{formErrors?.abhaNumber}</FormMessage>
                    </FormField>

                    <FormField label="ABHA Address">
                        <Input
                            className="text-sm py-1 px-2"
                            name="abhaAddress"
                            value={form.abha?.abhaAddress || ""}
                            onChange={onObjectChange("abha")}
                        />
                        <FormMessage>{formErrors?.abhaAddress}</FormMessage>
                    </FormField>
                </div>
            </div>

            {/* Facility Details */}
            {!isEditMode && (
                <div className="mb-3 bg-gray-50 rounded-md shadow-sm p-3">
                    <h3 className="text-base font-medium mb-3">Facility Details</h3>
                    <div className="grid grid-cols-1">
                        <FormField label="Facility" required>
                            <FacilitySelector
                                name="facilityId"
                                value={form.facilityId || ""}
                                onChange={(value) => onChange({ target: { name: "facilityId", value } } as React.ChangeEvent<HTMLInputElement>)}
                            />
                            <FormMessage>{formErrors["facilityId"]}</FormMessage>
                        </FormField>
                    </div>
                </div>
            )}

            {/* Basic Info */}
            <div className="mb-3 -gray-200 rounded-md shadow-sm p-3 bg-gray-50">
                <h3 className="text-base font-medium mb-3">Basic Info</h3>
                <div className="grid grid-cols-1 md:grid-cols-5 gap-3">
                    <FormField label="Title" required>
                        <Select className="text-sm py-1 px-2" name="title" value={form.title || ""} onChange={onChange}>
                            <option value="">Select</option>
                            {titleOptions.map(t => <option key={t} value={t}>{t}</option>)}
                        </Select>
                        <FormMessage>{formErrors["title"]}</FormMessage>
                    </FormField>

                    <FormField label="First Name" required>
                        <Input className="text-sm py-1 px-2" name="firstName" value={form.firstName || ""} onChange={onChange} />
                        <FormMessage>{formErrors["firstName"]}</FormMessage>
                    </FormField>

                    <FormField label="Middle Name">
                        <Input className="text-sm py-1 px-2" name="middleName" value={form.middleName || ""} onChange={onChange} />
                        <FormMessage>{formErrors["middleName"]}</FormMessage>
                    </FormField>

                    <FormField label="Last Name" required>
                        <Input className="text-sm py-1 px-2" name="lastName" value={form.lastName || ""} onChange={onChange} />
                        <FormMessage>{formErrors["lastName"]}</FormMessage>
                    </FormField>

                    <FormField label="Date of Birth" required>
                        <Calendar className="text-sm py-1 px-2" name="dateOfBirth" value={form.dateOfBirth || ""} onChange={onChange} />
                        <FormMessage>{formErrors["dateOfBirth"]}</FormMessage>
                    </FormField>

                    <FormField label="Age">
                        <Input className="text-sm py-1 px-2" name="age" value={form.age ?? ""} readOnly />
                    </FormField>

                    <FormField label="Gender" required>
                        <Select className="text-sm py-1 px-2" name="gender" value={form.gender || ""} onChange={onChange}>
                            <option value="">Select</option>
                            {genderOptions.map(g => <option key={g} value={g}>{g}</option>)}
                        </Select>
                        <FormMessage>{formErrors["gender"]}</FormMessage>
                    </FormField>

                    <FormField label="Blood Group">
                        <Select className="text-sm py-1 px-2" name="bloodGroup" value={form.bloodGroup || ""} onChange={onChange}>
                            <option value="">Select</option>
                            {bloodGroupOptions.map(bg => <option key={bg} value={bg}>{bg}</option>)}
                        </Select>
                    </FormField>

                    <FormField label="Marital Status">
                        <Select className="text-sm py-1 px-2" name="maritalStatus" value={form.maritalStatus || ""} onChange={onChange}>
                            <option value="">Select</option>
                            {maritalStatusOptions.map(ms => <option key={ms} value={ms}>{ms}</option>)}
                        </Select>
                    </FormField>

                    <FormField label="Identifier Type" required>
                        <Select
                            className="text-sm py-1 px-2"
                            name="identifierType"
                            value={form.identifierType || ""}
                            onChange={onChangeWithIdentifierCheck}
                        >
                            <option value="">Select</option>
                            {identifierTypeOptions.map((type) => (
                                <option key={type} value={type}>{type}</option>
                            ))}
                        </Select>
                        <FormMessage>{formErrors["identifierType"]}</FormMessage>
                    </FormField>

                    <FormField label="Identifier Number" required>
                        <Input
                            className="text-sm py-1 px-2"
                            name="identifierNumber"
                            value={form.identifierNumber || ""}
                            onChange={onChangeWithIdentifierCheck}
                            placeholder="Enter identifier value"
                        />
                        <FormMessage>{formErrors["identifierNumber"]}</FormMessage>
                    </FormField>
                </div>
            </div>
        </>
    );
};
