import React from "react";
import { FormField } from "../../../commonfields/FormField";
import { Input } from "../../../commonfields/Input";
import { Select } from "../../../commonfields/Select";
import { Calendar } from "../../../commonfields/Calendar";
import FormMessage from "../../../commonfields/FormMessage";
import {
    titleOptions,
    genderOptions,
    bloodGroupOptions,
    maritalStatusOptions,
    identifierTypeOptions,
} from "../../../types/patientenums";
import type { PatientRegistrationPayload } from "../../../types/patient";

type Props = {
    form: PatientRegistrationPayload;
    formErrors: Record<string, string>;
    isEditMode: boolean;
    onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    onChangeWithIdentifierCheck: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    onObjectChange: (key: keyof PatientRegistrationPayload) => (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
};

export const PatientBasicInfoSection: React.FC<Props> = ({
    form,
    formErrors,
    isEditMode,
    onChange,
    onChangeWithIdentifierCheck,
    onObjectChange,
}) => {
    return (
        <div className="space-y-8">
            {/* ABHA Details */}
            <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
                <div className="bg-gradient-to-r from-indigo-50 to-blue-50 px-6 py-4 border-b border-gray-200">
                    <div className="flex items-center space-x-3">
                        <div className="w-2 h-8 bg-gradient-to-b from-indigo-500 to-blue-600 rounded-full"></div>
                        <h3 className="text-lg font-semibold text-gray-800">ABHA Details</h3>
                    </div>
                </div>
                <div className="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField label="ABHA Number">
                        <Input
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                            name="abhaNumber"
                            value={form.abha?.abhaNumber || ""}
                            onChange={onObjectChange("abha")}
                        />
                        <FormMessage>{formErrors?.abhaNumber}</FormMessage>
                    </FormField>
                    <FormField label="ABHA Address">
                        <Input
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                            name="abhaAddress"
                            value={form.abha?.abhaAddress || ""}
                            onChange={onObjectChange("abha")}
                        />
                        <FormMessage>{formErrors?.abhaAddress}</FormMessage>
                    </FormField>
                </div>
            </div>


            {/* Identifier Details - Matched with 2 Columns */}
            <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden mb-6">
                <div className="bg-gradient-to-r from-purple-50 to-violet-50 px-6 py-4 border-b border-gray-200">
                    <div className="flex items-center space-x-3">
                        <div className="w-2 h-8 bg-gradient-to-b from-purple-500 to-violet-600 rounded-full"></div>
                        <h3 className="text-lg font-semibold text-gray-800">Identifier Details</h3>
                    </div>
                </div>
                <div className="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField label="Identifier Type">
                        <Select
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                            name="identifierType"
                            value={form.identifierType || ""}
                            onChange={onChangeWithIdentifierCheck}
                        >
                            <option value="">Select</option>
                            {identifierTypeOptions.map((type) => (
                                <option key={type} value={type}>{type}</option>
                            ))}
                        </Select>
                        <FormMessage>{formErrors["identifierType"]}</FormMessage>
                    </FormField>

                    <FormField label="Identifier Number">
                        <Input
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                            name="identifierNumber"
                            value={form.identifierNumber || ""}
                            onChange={onChangeWithIdentifierCheck}
                            placeholder="Enter identifier value"
                        />
                        <FormMessage>{formErrors["identifierNumber"]}</FormMessage>
                    </FormField>
                </div>
            </div>



            {/* Basic Info */}
            <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
                <div className="bg-gradient-to-r from-purple-50 to-violet-50 px-6 py-4 border-b border-gray-200">
                    <div className="flex items-center space-x-3">
                        <div className="w-2 h-8 bg-gradient-to-b from-purple-500 to-violet-600 rounded-full"></div>
                        <h3 className="text-lg font-semibold text-gray-800">Basic Info</h3>
                    </div>
                </div>
                <div className="p-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
                    <FormField label="Title" required>
                        <Select
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                            name="title"
                            value={form.title || ""}
                            onChange={onChange}
                        >
                            <option value="">Select</option>
                            {titleOptions.map(t => <option key={t} value={t}>{t}</option>)}
                        </Select>
                        <FormMessage>{formErrors["title"]}</FormMessage>
                    </FormField>

                    <FormField label="First Name" required>
                        <Input
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                            name="firstName"
                            value={form.firstName || ""}
                            onChange={onChange}
                        />
                        <FormMessage>{formErrors["firstName"]}</FormMessage>
                    </FormField>

                    <FormField label="Middle Name">
                        <Input
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                            name="middleName"
                            value={form.middleName || ""}
                            onChange={onChange}
                        />
                        <FormMessage>{formErrors["middleName"]}</FormMessage>
                    </FormField>

                    <FormField label="Last Name" required>
                        <Input
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                            name="lastName"
                            value={form.lastName || ""}
                            onChange={onChange}
                        />
                        <FormMessage>{formErrors["lastName"]}</FormMessage>
                    </FormField>

                    <FormField label="Date of Birth" required>
                        <Calendar
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                            name="dateOfBirth"
                            value={form.dateOfBirth || ""}
                            onChange={onChange}
                        />
                        <FormMessage>{formErrors["dateOfBirth"]}</FormMessage>
                    </FormField>

                    <FormField label="Age">
                        <Input
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg bg-gray-100"
                            name="age"
                            value={form.age ?? ""}
                            readOnly
                        />
                        <FormMessage>{formErrors["age"]}</FormMessage>
                    </FormField>


                    <FormField label="Gender" required>
                        <Select
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                            name="gender"
                            value={form.gender || ""}
                            onChange={onChange}
                        >
                            <option value="">Select</option>
                            {genderOptions.map(g => <option key={g} value={g}>{g}</option>)}
                        </Select>
                        <FormMessage>{formErrors["gender"]}</FormMessage>
                    </FormField>

                    <FormField label="Blood Group">
                        <Select
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                            name="bloodGroup"
                            value={form.bloodGroup || ""}
                            onChange={onChange}
                        >
                            <option value="">Select</option>
                            {bloodGroupOptions.map(bg => <option key={bg} value={bg}>{bg}</option>)}
                        </Select>
                    </FormField>

                    <FormField label="Marital Status">
                        <Select
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                            name="maritalStatus"
                            value={form.maritalStatus || ""}
                            onChange={onChange}
                        >
                            <option value="">Select</option>
                            {maritalStatusOptions.map(ms => <option key={ms} value={ms}>{ms}</option>)}
                        </Select>
                    </FormField>

                </div>
            </div>
        </div>
    );
};
