import React from "react";
import { FormField } from "../../../commonfields/FormField";
import { Input } from "../../../commonfields/Input";
import { Select } from "../../../commonfields/Select";
import FormMessage from "../../../commonfields/FormMessage";
import {
    contactModeOptions,
    phonePrefOptions,
    addressTypeOptions,
} from "../../../types/patientenums";
import type { PatientRegistrationPayload } from "../../../types/patient";
import { addArrayItem, removeArrayItem } from "../../../hooks/useFormHandlers";
import { showError } from "../../../utils/toastUtils";

type Props = {
    form: PatientRegistrationPayload;
    setForm: React.Dispatch<React.SetStateAction<PatientRegistrationPayload>>;
    formErrors: Record<string, string>;
    onContactChange: (index: number) => (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    onAddressChange: (index: number) => (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    onAddressTypeChange: (index: number) => (e: React.ChangeEvent<HTMLSelectElement>) => void;
    addAddress: () => void;
    onCheckboxChange: (index: number) => void;
    presentSameAsPermanent: boolean;
};

export const PatientContactAddressSection: React.FC<Props> = ({
    form,
    setForm,
    formErrors,
    onContactChange,
    onAddressChange,
    onAddressTypeChange,
    addAddress,
    onCheckboxChange,
    presentSameAsPermanent,
}) => {
    return (
        <div className="space-y-8">
            {/* Contact Information */}
            <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
                <div className="bg-gradient-to-r from-sky-50 to-blue-50 px-6 py-4 border-b border-gray-200">
                    <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-3">
                            <div className="w-2 h-8 bg-gradient-to-b from-sky-500 to-blue-600 rounded-full"></div>
                            <h3 className="text-lg font-semibold text-gray-800">Contact Information</h3>
                        </div>
                        <button
                            type="button"
                            className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-sky-500 to-blue-600 rounded-lg hover:from-sky-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 transform transition-all duration-200 hover:scale-105 shadow-md"
                            onClick={() => addArrayItem(setForm, "contacts", {})}
                        >
                            + Add Contact
                        </button>
                    </div>
                </div>

                <div className="p-6 space-y-4">
                    {form.contacts?.map((contact, index) => {
                        const isPrimary = index === 0;

                        return (
                            <div
                                key={index}
                                className="bg-gray-50 rounded-lg p-6 border border-gray-200 hover:shadow-md transition-shadow duration-200 relative"
                            >
                                <div className={`grid gap-4 ${isPrimary ? 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4' : 'grid-cols-1 sm:grid-cols-2'}`}>
                                    <FormField label= {isPrimary ? "Primary Phone Number " : `Alternative Number ${(index-1)*2+1}`} required={isPrimary}>
                                        <Input
                                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                            name="phoneNumber"
                                            value={contact.phoneNumber || ""}
                                            onChange={onContactChange(index)}
                                           
                                        />
                                        <FormMessage>{formErrors?.[`contacts.${index}.phoneNumber`]}</FormMessage>
                                    </FormField>

                                    {isPrimary ? (
                                        <>
                                            <FormField label="Email">
                                                <Input
                                                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                                    name="email"
                                                    value={contact.email || ""}
                                                    onChange={onContactChange(index)}
                                                />
                                                <FormMessage>{formErrors?.[`contacts.${index}.email`]}</FormMessage>
                                            </FormField>

                                            <FormField label="Preferred Contact Mode">
                                                <Select
                                                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                                    name="preferredContactMode"
                                                    value={contact.preferredContactMode || ""}
                                                    onChange={onContactChange(index)}
                                                >
                                                    <option value="">Select</option>
                                                    {contactModeOptions.map(mode => (
                                                        <option key={mode} value={mode}>{mode}</option>
                                                    ))}
                                                </Select>
                                                <FormMessage>{formErrors?.[`contacts.${index}.preferredContactMode`]}</FormMessage>
                                            </FormField>

                                            <FormField label="Phone Contact Preference">
                                                <Select
                                                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                                    name="phoneContactPreference"
                                                    value={contact.phoneContactPreference || ""}
                                                    onChange={onContactChange(index)}
                                                >
                                                    <option value="">Select</option>
                                                    {phonePrefOptions.map(pref => (
                                                        <option key={pref} value={pref}>{pref}</option>
                                                    ))}
                                                </Select>
                                                <FormMessage>{formErrors?.[`contacts.${index}.phoneContactPreference`]}</FormMessage>
                                            </FormField>

                                            <FormField label="Consent to Share">
                                                <div className="flex items-center space-x-2 mt-2">
                                                    <input
                                                        type="checkbox"
                                                        id={`consent-${index}`}
                                                        name="consentToShare"
                                                        checked={contact.consentToShare === true}
                                                        onChange={(e) => {
                                                            const event = {
                                                                target: {
                                                                    name: 'consentToShare',
                                                                    value: e.target.checked,
                                                                },
                                                            } as any;
                                                            onContactChange(index)(event);
                                                        }}
                                                        className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                                    />
                                                    <label htmlFor={`consent-${index}`} className="text-sm text-gray-700">
                                                        Yes, I consent to share
                                                    </label>
                                                </div>
                                            </FormField>
                                        </>
                                    ) : (
                                        <>
                                            <FormField label={`Alternate Number ${(index - 1) * 2 + 2}`}>
                                                <Input
                                                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                                    name="mobileNumber"
                                                    value={contact.mobileNumber || ""}
                                                    onChange={onContactChange(index)}
                                                />
                                                <FormMessage>{formErrors?.[`contacts.${index}.mobileNumber`]}</FormMessage>
                                            </FormField>
                                        </>
                                    )}
                                </div>

                                {/* Remove button for non-primary contacts */}
                                {!isPrimary && (
                                    <div className="flex justify-end mt-4">
                                        <button
                                            type="button"
                                            className="inline-flex items-center px-4 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500"
                                            onClick={() => removeArrayItem(setForm, "contacts", index)}
                                        >
                                            Remove
                                        </button>
                                    </div>
                                )}
                            </div>
                        );
                    })}
                </div>
            </div>


            {/* Address Section */}
            <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
                <div className="bg-gradient-to-r from-amber-50 to-orange-50 px-6 py-4 border-b border-gray-200">
                    <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-3">
                            <div className="w-2 h-8 bg-gradient-to-b from-amber-500 to-orange-600 rounded-full"></div>
                            <h3 className="text-lg font-semibold text-gray-800">Address Information</h3>
                        </div>
                        <button
                            type="button"
                            className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-amber-500 to-orange-600 rounded-lg hover:from-amber-600 hover:to-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transform transition-all duration-200 hover:scale-105 shadow-md"
                            onClick={addAddress}
                        >
                            + Add Address
                        </button>
                    </div>
                </div>
                <div className="p-6 space-y-4">
                    {form.addresses?.map((address, index) => {
                        const isPresent = address.addressType === "Present";
                        const permanentAddressExists = form.addresses.some(a => a.addressType === "Permanent");

                        return (
                            <div key={index} className="bg-gray-50 rounded-lg p-6 border border-gray-200 hover:shadow-md transition-shadow duration-200">
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                    <FormField label="Address Type">
                                        <Select
                                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                            name="addressType"
                                            value={address.addressType || ""}
                                            onChange={onAddressTypeChange(index)}
                                            disabled={presentSameAsPermanent && isPresent}
                                        >
                                            <option value="">Select</option>
                                            {addressTypeOptions.map(type => (
                                                <option key={type} value={type}>{type}</option>
                                            ))}
                                        </Select>
                                    </FormField>

                                    <FormField label="House/Flat No">
                                        <Input
                                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                            name="houseNoOrFlatNo"
                                            value={address.houseNoOrFlatNo || ""}
                                            onChange={onAddressChange(index)}
                                            disabled={presentSameAsPermanent && isPresent}
                                        />
                                    </FormField>

                                    <FormField label="Locality/Sector">
                                        <Input
                                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                            name="localityOrSector"
                                            value={address.localityOrSector || ""}
                                            onChange={onAddressChange(index)}
                                            disabled={presentSameAsPermanent && isPresent}
                                        />
                                    </FormField>

                                    <FormField label="City/Village">
                                        <Input
                                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                            name="cityOrVillage"
                                            value={address.cityOrVillage || ""}
                                            onChange={onAddressChange(index)}
                                            disabled={presentSameAsPermanent && isPresent}
                                        />
                                    </FormField>

                                    <FormField label="Pincode">
                                        <Input
                                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                            name="pincode"
                                            value={address.pincode || ""}
                                            onChange={onAddressChange(index)}
                                            disabled={presentSameAsPermanent && isPresent}
                                        />
                                        <FormMessage>{formErrors?.[`addresses.${index}.pincode`]}</FormMessage>
                                    </FormField>

                                    <FormField label="District">
                                        <Input
                                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                            name="districtId"
                                            value={address.districtId || ""}
                                            onChange={onAddressChange(index)}
                                            disabled={presentSameAsPermanent && isPresent}
                                        />
                                    </FormField>

                                    <FormField label="State">
                                        <Input
                                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                            name="stateId"
                                            value={address.stateId || ""}
                                            onChange={onAddressChange(index)}
                                            disabled={presentSameAsPermanent && isPresent}
                                        />
                                    </FormField>

                                    <FormField label="Country">
                                        <Input
                                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                            name="country"
                                            value={address.country || ""}
                                            onChange={onAddressChange(index)}
                                            disabled={presentSameAsPermanent && isPresent}
                                        />
                                    </FormField>
                                </div>

                                {isPresent && permanentAddressExists && (
                                    <div className="mt-4 flex items-center space-x-2">
                                        <input
                                            type="checkbox"
                                            id={`presentSameAsPermanent-${index}`}
                                            checked={presentSameAsPermanent}
                                            onChange={() => onCheckboxChange(index)}
                                        />
                                        <label htmlFor={`presentSameAsPermanent-${index}`} className="text-sm text-gray-700">
                                            Present address same as Permanent
                                        </label>
                                    </div>
                                )}

                                <div className="flex justify-end mt-4">
                                    <button
                                        type="button"
                                        className="inline-flex items-center px-4 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500"
                                        onClick={() => {
                                            if (form.addresses.length <= 1) {
                                                showError("Cannot remove", "At least one address must remain.");
                                                return;
                                            }
                                            removeArrayItem(setForm, "addresses", index);
                                        }}
                                        disabled={presentSameAsPermanent && isPresent}
                                    >
                                        Remove
                                    </button>
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>
        </div>
    );
};
