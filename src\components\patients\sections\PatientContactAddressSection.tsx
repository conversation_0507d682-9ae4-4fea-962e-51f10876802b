import React from "react";
import { FormField } from "../../../commonfields/FormField";
import { Input } from "../../../commonfields/Input";
import { Select } from "../../../commonfields/Select";
import FormMessage from "../../../commonfields/FormMessage";
import {
    contactModeOptions,
    phonePrefOptions,
    addressTypeOptions,
    AddressType,
} from "../../../types/patientenums";
import type { PatientRegistrationPayload } from "../../../types/patient";
import { addArrayItem, removeArrayItem } from "../../../hooks/useFormHandlers";
import { showError } from "../../../utils/toastUtils";

type Props = {
    form: PatientRegistrationPayload;
    setForm: React.Dispatch<React.SetStateAction<PatientRegistrationPayload>>;
    formErrors: Record<string, string>;
    onContactChange: (index: number) => (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    onAddressChange: (index: number) => (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    onAddressTypeChange: (index: number) => (e: React.ChangeEvent<HTMLSelectElement>) => void;
    addAddress: () => void;
    onCheckboxChange: (index: number) => void;
    presentSameAsPermanent: boolean;
};

export const PatientContactAddressSection: React.FC<Props> = ({
    form,
    setForm,
    formErrors,
    onContactChange,
    onAddressChange,
    onAddressTypeChange,
    addAddress,
    onCheckboxChange,
    presentSameAsPermanent,
}) => {
    return (
        <>
            {/* Contacts */}
            <div>
                <div className="mb-3 -gray-200 rounded-md shadow-sm p-3 bg-gray-50">
                    <div className="flex justify-between items-center mb-2">
                        <h3 className="text-base font-medium">Contacts</h3>
                        <button
                            type="button"
                            className="text-xs text-indigo-600 font-medium -indigo-300 px-2 py-1 rounded hover:bg-indigo-50 transition"
                            onClick={() => addArrayItem(setForm, "contacts", {})}
                        >
                            + Add
                        </button>
                    </div>
                    {form.contacts?.map((contact, index) => (
                        <div key={index} className="grid grid-cols-1 md:grid-cols-5 gap-3 p-3 mb-2 bg-white rounded-md">
                            <FormField label="Primary Phone Number" required={index === 0}>
                                <Input className="text-sm py-1 px-2" name="phoneNumber" value={contact.phoneNumber || ""} onChange={onContactChange(index)} />
                                <FormMessage>
                                    {formErrors?.[`contacts.${index}.phoneNumber`] || (index === 0 && formErrors?.["contacts.0.phoneNumber"])}
                                </FormMessage>
                            </FormField>
                            <FormField label="Alternate Number">
                                <Input className="text-sm py-1 px-2" name="mobileNumber" value={contact.mobileNumber || ""} onChange={onContactChange(index)} />
                                <FormMessage>{formErrors?.[`contacts.${index}.mobileNumber`]}</FormMessage>
                            </FormField>
                            <FormField label="Email">
                                <Input className="text-sm py-1 px-2" name="email" value={contact.email || ""} onChange={onContactChange(index)} />
                                <FormMessage>{formErrors?.[`contacts.${index}.email`]}</FormMessage>
                            </FormField>
                            <FormField label="Preferred Contact Mode">
                                <Select className="text-sm py-1 px-2" name="preferredContactMode" value={contact.preferredContactMode || ""} onChange={onContactChange(index)}>
                                    <option value="">Select</option>
                                    {contactModeOptions.map(mode => (
                                        <option key={mode} value={mode}>{mode}</option>
                                    ))}
                                </Select>
                            </FormField>
                            <FormField label="Phone Contact Preference">
                                <Select className="text-sm py-1 px-2" name="phoneContactPreference" value={contact.phoneContactPreference || ""} onChange={onContactChange(index)}>
                                    <option value="">Select</option>
                                    {phonePrefOptions.map(p => (
                                        <option key={p} value={p}>{p}</option>
                                    ))}
                                </Select>
                            </FormField>
                            <FormField label="Consent to Share">
                                <Select className="text-sm py-1 px-2" name="consentToShare" value={String(contact.consentToShare)} onChange={onContactChange(index)}>
                                    <option value="true">Yes</option>
                                    <option value="false">No</option>
                                </Select>
                            </FormField>
                            <div className="w-full flex justify-end mt-1 md:col-span-5">
                                <button
                                    type="button"
                                    className="text-xs text-red-600 font-medium -red-300 px-3 py-1 rounded hover:bg-red-50 transition"
                                    onClick={() => removeArrayItem(setForm, "contacts", index)}
                                >
                                    Remove
                                </button>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Addresses */}
            <div>
                <div className="mb-3 rounded-md shadow-sm p-3 bg-gray-50">
                    <div className="flex justify-between items-center mb-2">
                        <h3 className="text-base font-medium">Addresses</h3>
                        <button
                            type="button"
                            className="text-xs text-indigo-600 font-medium px-2 py-1 rounded hover:bg-indigo-50 transition"
                            onClick={addAddress}
                        >
                            + Add
                        </button>
                    </div>

                    {form.addresses?.map((address, index) => {
                        const isPresent = address.addressType === "Present";
                        const permanentAddressExists = form.addresses.some(a => a.addressType === "Permanent");
                        return (
                            <div key={index} className="grid grid-cols-1 md:grid-cols-5 gap-3 p-3 mb-2 bg-white rounded-md">
                                <FormField label="Address Type">
                                    <Select
                                        className="text-sm py-1 px-2"
                                        name="addressType"
                                        value={address.addressType || ""}
                                        onChange={onAddressTypeChange(index)}
                                        disabled={presentSameAsPermanent && isPresent}
                                    >
                                        <option value="">Select</option>
                                        {addressTypeOptions.map(type => (
                                            <option key={type} value={type}>{type}</option>
                                        ))}
                                    </Select>
                                </FormField>

                                <FormField label="House/Flat No">
                                    <Input
                                        className="text-sm py-1 px-2"
                                        name="houseNoOrFlatNo"
                                        value={address.houseNoOrFlatNo || ""}
                                        onChange={onAddressChange(index)}
                                        disabled={presentSameAsPermanent && isPresent}
                                    />
                                </FormField>

                                <FormField label="Locality/Sector">
                                    <Input
                                        className="text-sm py-1 px-2"
                                        name="localityOrSector"
                                        value={address.localityOrSector || ""}
                                        onChange={onAddressChange(index)}
                                        disabled={presentSameAsPermanent && isPresent}
                                    />
                                </FormField>

                                <FormField label="City/Village">
                                    <Input
                                        className="text-sm py-1 px-2"
                                        name="cityOrVillage"
                                        value={address.cityOrVillage || ""}
                                        onChange={onAddressChange(index)}
                                        disabled={presentSameAsPermanent && isPresent}
                                    />
                                </FormField>

                                <FormField label="Pincode">
                                    <Input
                                        className="text-sm py-1 px-2"
                                        name="pincode"
                                        value={address.pincode || ""}
                                        onChange={onAddressChange(index)}
                                        disabled={presentSameAsPermanent && isPresent}
                                    />
                                    <FormMessage>{formErrors?.[`addresses.${index}.pincode`]}</FormMessage>
                                </FormField>

                                <FormField label="District">
                                    <Input
                                        className="text-sm py-1 px-2"
                                        name="districtId"
                                        value={address.districtId || ""}
                                        onChange={onAddressChange(index)}
                                        disabled={presentSameAsPermanent && isPresent}
                                    />
                                </FormField>

                                <FormField label="State">
                                    <Input
                                        className="text-sm py-1 px-2"
                                        name="stateId"
                                        value={address.stateId || ""}
                                        onChange={onAddressChange(index)}
                                        disabled={presentSameAsPermanent && isPresent}
                                    />
                                </FormField>

                                <FormField label="Country">
                                    <Input
                                        className="text-sm py-1 px-2"
                                        name="country"
                                        value={address.country || ""}
                                        onChange={onAddressChange(index)}
                                        disabled={presentSameAsPermanent && isPresent}
                                    />
                                </FormField>

                                {/* Checkbox for Present address same as Permanent */}
                                {isPresent && permanentAddressExists && (
                                    <div className="md:col-span-5 flex items-center space-x-2 mt-2">
                                        <input
                                            type="checkbox"
                                            id={`presentSameAsPermanent-${index}`}
                                            checked={presentSameAsPermanent}
                                            onChange={() => onCheckboxChange(index)}
                                        />
                                        <label htmlFor={`presentSameAsPermanent-${index}`}>
                                            Present address same as Permanent
                                        </label>
                                    </div>
                                )}

                                <div className="w-full flex justify-end mt-1 md:col-span-5">
                                    <button
                                        type="button"
                                        className="text-xs text-red-600 font-medium px-3 py-1 rounded hover:bg-red-50 transition"
                                        onClick={() => {
                                            if (form.addresses.length <= 1) {
                                                showError("Cannot remove", "At least one address must remain.");
                                                return;
                                            }
                                            removeArrayItem(setForm, "addresses", index);
                                        }}
                                        disabled={presentSameAsPermanent && isPresent}
                                    >
                                        Remove
                                    </button>
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>
        </>
    );
};
