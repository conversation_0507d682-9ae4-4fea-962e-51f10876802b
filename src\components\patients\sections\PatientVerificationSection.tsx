import React, { useState } from "react";
import AbhaInput from "../../verification/AbhaInput";
import AadhaarInput from "../../verification/AadhaarInput";

type Props = {
    isEditMode: boolean;
};

export const PatientVerificationSection: React.FC<Props> = ({ isEditMode }) => {
    const [abhaNumber, setAbhaNumber] = useState("");
    const [aadhaarNumber, setAadhaarNumber] = useState("");

    // Don't show verification section in edit mode
    if (isEditMode) {
        return null;
    }

    return (
        <div className="mb-3 bg-blue-50 rounded-md shadow-sm p-4 border border-blue-200">
            <h3 className="text-base font-medium mb-3 text-blue-800">Patient Verification</h3>
            {/* <p className="text-sm text-blue-600 mb-4">
                Verify existing patient records or generate ABHA details before registration
            </p> */}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <AbhaInput
                        abhaNumber={abhaNumber}
                        setAbhaNumber={setAbhaNumber}
                    />
                    <p className="mt-3 text-xs text-blue-500"> Use ABHA Number to verify existing patient records</p>
                </div>
                <div>
                    <AadhaarInput
                        aadhaarNumber={aadhaarNumber}
                        setAadhaarNumber={setAadhaarNumber}
                    />
                    <p className="mt-3 text-xs text-blue-500"> Use Aadhaar Number to generate OTP and create new ABHA profile</p>
                </div>
            </div>
        </div>
    );
};
