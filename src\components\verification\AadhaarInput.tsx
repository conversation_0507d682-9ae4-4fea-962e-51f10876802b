import React, { useState } from "react";
import { requestAadhaarOtp } from "../../services/abhaApis";
import { showError, showSuccess } from "../../utils/toastUtils";
import OtpPopup from "./OtpPopup";

type AadhaarInputProps = {
  aadhaarNumber: string;
  setAadhaarNumber: (value: string) => void;
};

const AadhaarInput: React.FC<AadhaarInputProps> = ({ aadhaarNumber, setAadhaarNumber }) => {
  const [showPopup, setShowPopup] = useState(false);
  const [txnId, setTxnId] = useState("");
  const [loading, setLoading] = useState(false);

  const handleGenerate = async () => {
    if (!aadhaarNumber || aadhaarNumber.length !== 12) {
      showError("", "Enter a valid 12-digit Aadhaar number");
      return;
    }

    setLoading(true);
    try {
      const { success, data, error } = await requestAadhaarOtp({ aadhaarNumber });

      if (success && data?.txnId) {
        showSuccess("OTP Requested", data.message || "OTP sent successfully.");
        setTxnId(data.txnId);
        setShowPopup(true);
      } else {
        showError("OTP Request Failed", error?.message || "Something went wrong.");
      }
    } catch (err: any) {
      showError("Server Error", err?.message || "Unexpected error.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <div className="flex items-center gap-2">
        <input
          type="text"
          maxLength={12}
          value={aadhaarNumber}
          onChange={(e) => {
            const numeric = e.target.value.replace(/\D/g, "");
            setAadhaarNumber(numeric);
          }}
          placeholder="Enter Aadhaar Number"
          className="border rounded-md px-3 py-1.5 text-sm shadow-sm "
        />
        <button
          type="button"
          onClick={handleGenerate}
          disabled={loading || aadhaarNumber.length !== 12}
          className={`px-4 py-1.5 text-sm rounded-md transition whitespace-nowrap
            ${loading || aadhaarNumber.length !== 12
              ? 'bg-gray-400 text-white cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 text-white cursor-pointer'}`}
        >
          {loading ? "Sending..." : "Generate"}
        </button>
      </div>

      {showPopup && (
        <OtpPopup
          txnId={txnId}
          onClose={() => setShowPopup(false)}
        />
      )}
    </>
  );
};

export default AadhaarInput;
