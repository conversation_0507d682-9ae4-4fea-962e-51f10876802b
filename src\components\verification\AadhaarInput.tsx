import React, { useState } from "react";
import { requestAadhaarOtp } from "../../services/abhaApis"; // Adjust path as needed
import { showError, showSuccess } from "../../utils/toastUtils";
import OtpPopup from "./OtpPopup";

type AadhaarInputProps = {
    aadhaarNumber: string;
    setAadhaarNumber: (value: string) => void;
};

const AadhaarInput: React.FC<AadhaarInputProps> = ({ aadhaarNumber, setAadhaarNumber }) => {
    const [showPopup, setShowPopup] = useState(false);
    const [txnId, setTxnId] = useState("");
    const [loading, setLoading] = useState(false);
    const [consentGiven, setConsentGiven] = useState(false); // ✅ Consent state



    const handleGenerate = async () => {
        if (!aadhaarNumber || aadhaarNumber.length !== 12) {
            showError("", "Enter a valid 12-digit Aadhaar number");
            return;
        }

        setLoading(true); // START LOADING
        try {
            const { success, data, error } = await requestAadhaarOtp({ aadhaarNumber });

            if (success && data?.txnId) {
                showSuccess("OTP Requested", data.message || "OTP sent successfully.");
                setTxnId(data.txnId);
                setShowPopup(true);
            } else {
                const errorMessage = typeof error === "string"
                    ? error
                    : error?.details || error?.message || "Something went wrong while requesting OTP.";

                showError("OTP Request Failed", errorMessage);
            }
        } catch (err: any) {
            const extractedError = err?.response?.data?.details
                || err?.response?.data?.message
                || err?.message
                || "Unexpected server error occurred.";
            showError("Server Error", extractedError);
        } finally {
            setLoading(false); // END LOADING
        }
    };


    return (
        <>
            <div className="flex-1">
                <label className="block text-sm font-medium mb-1">Aadhaar Number</label>
                <div className="relative">
                    <input
                        type="text"
                        maxLength={12}
                        value={aadhaarNumber}
                        onChange={(e) => {
                            const numericValue = e.target.value.replace(/\D/g, ""); // Remove all non-digit characters
                            setAadhaarNumber(numericValue);
                        }}

                        placeholder="Enter Aadhaar Number"
                        className="w-full border rounded-md pr-24 pl-3 py-1.5 text-sm shadow-sm"
                    />
                    <button
                        type="button"
                        onClick={handleGenerate}
                        disabled={loading || aadhaarNumber.length !== 12 || !consentGiven} // ✅ Disable logic
                        className={`absolute top-1/2 -translate-y-1/2 right-1 px-3 py-1 text-sm rounded-md transition
                                ${(loading || aadhaarNumber.length !== 12 || !consentGiven)
                                ? 'bg-gray-400 text-white cursor-not-allowed'
                                : 'bg-blue-600 hover:bg-blue-700 text-white cursor-pointer'}`}
                    >
                        {loading ? "Sending..." : "Generate"}
                    </button>

                </div>
                {/* ✅ Consent Checkbox */}
                <div className="mt-2 text-sm flex items-start gap-2">
                    <input
                        type="checkbox"
                        id="consent"
                        checked={consentGiven}
                        onChange={(e) => setConsentGiven(e.target.checked)}
                        className="mt-0.5"
                    />
                    <label htmlFor="consent">
                        I give my consent to share Aadhaar details with the system for verification.
                    </label>
                </div>
            </div>

            {showPopup && (
                <OtpPopup
                    txnId={txnId}
                    onClose={() => setShowPopup(false)}
                />
            )}
        </>
    );
};

export default AadhaarInput;
