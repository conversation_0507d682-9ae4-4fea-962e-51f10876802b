import React, { useState } from "react";
import { verifyAbhaNumber } from "../../services/abhaApis";
import { showError, showSuccess } from "../../utils/toastUtils";
import { usePatientFormStore } from "../../store/patientFormStore";
import { mapAbhaProfileToPatient } from "../../utils/mapAbhaToPatient";

type AbhaInputProps = {
  abhaNumber: string;
  setAbhaNumber: (value: string) => void;
};

const AbhaInput: React.FC<AbhaInputProps> = ({ abhaNumber, setAbhaNumber }) => {
  const [loading, setLoading] = useState(false);

  const handleVerify = async () => {
    if (!abhaNumber || abhaNumber.length !== 14) {
      showError("", "Enter a valid 14-digit ABHA number");
      return;
    }

    setLoading(true);
    try {
      const { success, data, error } = await verifyAbhaNumber({ abhaNumber });

      if (success && data && data.ABHAProfile) {
        console.log("ABHA Verification Response:", data);
        const patientData = mapAbhaProfileToPatient(data.ABHAProfile);
        console.log("Mapped Patient Data:", patientData);

        if (patientData) {
          usePatientFormStore.getState().setQuickFormData(patientData);
          showSuccess("ABHA Verification Successful", "Patient data populated from ABHA profile");
        } else {
          showError("Mapping Failed", "Could not map ABHA profile to patient format.");
        }
      } else {
        showError("ABHA Verification Failed", error || "Could not verify ABHA number");
      }
    } catch (err) {
      console.error("Unexpected error during ABHA verification:", err);
      showError("Verification Error", "Unable to verify ABHA number");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex-1">
      <label className="block text-sm font-medium mb-1">ABHA Number</label>
      <div className="relative">
        <input
          type="text"
          value={abhaNumber}
          onChange={(e) => setAbhaNumber(e.target.value)}
          placeholder="Enter ABHA Number"
          className="w-full border rounded-md pr-20 pl-3 py-1.5 text-sm shadow-sm"
          disabled={loading}
        />
        <button
          type="button"
          onClick={handleVerify}
          disabled={loading}
          className="absolute top-1/2 -translate-y-1/2 right-1 bg-green-600 text-white px-3 py-1 text-sm rounded-md hover:bg-green-700 transition disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? "..." : "Verify"}
        </button>
      </div>
    </div>
  );
};

export default AbhaInput;
