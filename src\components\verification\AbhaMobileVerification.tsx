import React, { useState, useEffect } from "react";
import { FaMobileAlt, <PERSON><PERSON><PERSON><PERSON>, <PERSON>aUser<PERSON>heck } from "react-icons/fa";
import { requestOtp, verifyOtp, verifyUser } from "../../services/abhaApis";
import { FormField } from "../../commonfields/FormField";
import { Input } from "../../commonfields/Input";
import { usePatientFormStore } from "../../store/patientFormStore";
import { mapAbhaProfileToPatient } from "../../utils/mapAbhaToPatient";
import { showError, showSuccess } from "../../utils/toastUtils";

type Props = {
  isOpen: boolean;
  onClose: () => void;
  inputValue: string;
  mode: "mobile" | "abha";
};

export const AbhaMobileVerification: React.FC<Props> = ({
  isOpen,
  onClose,
  inputValue,
  mode,
}) => {
  /* ------------- local state ------------- */
  const [step, setStep] = useState(0);
  const [txnId, setTxnId] = useState("");
  const [otp, setOtp] = useState("");
  const [token, setToken] = useState("");
  const [abhaNumber, setAbhaNumber] = useState("");
  const [userInfo, setUserInfo] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const setQuickFormData = usePatientFormStore(
    (state) => state.setQuickFormData
  );

  /* -------- lock / unlock body scroll -------- */
  useEffect(() => {
    if (isOpen) document.body.style.overflow = "hidden";
    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);

  /* ---------------- helpers ---------------- */
  const autoRequestOtp = async () => {
    setLoading(true);
    setError("");
    try {
      const payload =
        mode === "mobile" ? { mobile: inputValue } : { abhaNumber: inputValue };
      const res = await requestOtp(payload);
      setTxnId(res.txnId);
      setStep(1);
      showSuccess("OTP Sent", res.message || "OTP sent successfully.");
    } catch (err: any) {
      const msg = err?.response?.data?.message || err.message;
      showError("OTP Request Failed", msg);
      setError(msg);
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyOtp = async () => {
    setLoading(true);
    setError("");
    try {
      const res = await verifyOtp({ txnId, otp, type: mode });

      if (!res.success) {
        showError("OTP Verification Failed", res.error?.message);
        return;
      }

      const data = res.data;

      if (mode === "mobile") {
        const abha = data?.accounts?.[0]?.ABHANumber;
        if (!abha) throw new Error("ABHA Number not found.");
        setAbhaNumber(abha);
        setToken(data?.token);
        setStep(2);
      } else {
        // ABHA mode returns profile directly
        const patientData = mapAbhaProfileToPatient(data);
        if (patientData) {
          setQuickFormData(patientData);
          setUserInfo(data);
          setStep(2);
        } else {
          throw new Error("Could not map ABHA profile.");
        }
      }
    } catch (err: any) {
      const msg =
        err?.response?.data?.message || err.message || "OTP verification error.";
      setError(msg);
      showError("Verification Failed", msg);
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyUser = async () => {
    setLoading(true);
    setError("");
    try {
      const res = await verifyUser({ txnId, abhaNumber }, token);
      const patientData = mapAbhaProfileToPatient(res);
      if (patientData) {
        setQuickFormData(patientData);
        setUserInfo(res);
        showSuccess("ABHA Verified", "Patient data populated.");
      } else {
        throw new Error("Could not map ABHA profile.");
      }
    } catch (err: any) {
      const msg = err?.response?.data?.message || err.message;
      setError(msg);
      showError("User Verification Failed", msg);
    } finally {
      setLoading(false);
    }
  };

  const StepStatus = ({ index, label, Icon }: any) => (
    <div className="flex flex-col items-center gap-1 text-sm">
      <Icon
        className={`text-2xl ${
          step > index
            ? "text-emerald-500"
            : step === index
            ? "text-indigo-500"
            : "text-gray-400"
        }`}
      />
      <span
        className={
          step > index ? "font-medium text-emerald-600" : "text-slate-600"
        }
      >
        {label}
      </span>
    </div>
  );

  /* --------------- render --------------- */
  if (!isOpen) return null;

  return (
    /* overlay */
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-slate-200/60 backdrop-blur-md">
      {/* card */}
      <div className="relative w-full max-w-md animate-[fadeIn_.25s_ease-out] rounded-xl bg-white p-6 shadow-xl ring-1 ring-indigo-100">
        {/* close */}
        <button
          className="absolute right-3 top-2 text-slate-400 hover:text-red-500 focus:outline-none"
          onClick={onClose}
          aria-label="Close"
        >
          &times;
        </button>

        {/* heading */}
        <h2 className="mb-4 text-xl font-semibold tracking-wide text-indigo-700">
          ABHA&nbsp;{mode === "mobile" ? "Mobile" : "Number"} Login
        </h2>

        {/* progress */}
        <div className="mb-6 flex items-center justify-between">
          <StepStatus index={0} label="Start" Icon={FaMobileAlt} />
          <span className="h-0.5 w-8 bg-slate-300" />
          <StepStatus index={1} label="OTP" Icon={FaKey} />
          <span className="h-0.5 w-8 bg-slate-300" />
          <StepStatus index={2} label="Verify" Icon={FaUserCheck} />
        </div>

        {/* -------- Step 0 -------- */}
        {step === 0 && (
          <FormField label="Enter Number" required error={error}>
            <Input value={inputValue} readOnly />
            <button
              className="btn btn-primary mt-4 w-full"
              onClick={autoRequestOtp}
              disabled={loading}
            >
              {loading ? "Sending OTP..." : "Send OTP"}
            </button>
          </FormField>
        )}

        {/* -------- Step 1 -------- */}
        {step === 1 && (
          <FormField label="Enter OTP" required error={error}>
            <Input
              type="text"
              value={otp}
              onChange={(e) => setOtp(e.target.value)}
            />
            <button
              className="btn btn-primary mt-4 w-full"
              onClick={handleVerifyOtp}
              disabled={loading}
            >
              {loading ? "Verifying OTP..." : "Verify OTP"}
            </button>
          </FormField>
        )}

        {/* -------- Step 2 (mobile) -------- */}
        {step === 2 && mode === "mobile" && (
          <FormField label="ABHA Number" required error={error}>
            <Input value={abhaNumber} readOnly />
            <button
              className="btn btn-primary mt-4 w-full"
              onClick={handleVerifyUser}
              disabled={loading}
            >
              {loading ? "Verifying User..." : "Verify User"}
            </button>
          </FormField>
        )}

        {/* -------- Verified summary -------- */}
        {step === 2 && userInfo && (
          <div className="mt-4 rounded-lg border border-emerald-300 bg-emerald-50 p-4 text-sm">
            <p className="font-medium text-emerald-700">✅ Verified</p>
            <p>
              <strong>Name:</strong> {userInfo.name}
            </p>
            <p>
              <strong>Mobile:</strong> {userInfo.mobile}
            </p>
            <p>
              <strong>Address:</strong> {userInfo.address}
            </p>

            <button
              className="btn btn-success mt-4 w-full"
              onClick={() => {
                onClose();
                document
                  .getElementById("patient-registration-form")
                  ?.scrollIntoView({ behavior: "smooth", block: "start" });
              }}
            >
              Proceed to Registration
            </button>
          </div>
        )}
      </div>
    </div>
  );
};
