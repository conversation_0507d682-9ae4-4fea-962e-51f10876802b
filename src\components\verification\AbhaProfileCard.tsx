// import React from "react";

// type Props = {
//     profile: any;
// };

// const AbhaProfileCard: React.FC<Props> = ({ profile }) => {
//     return (
//         <div className="bg-gray-100 border border-gray-300 rounded-xl p-5 shadow-inner">
//             <h3 className="text-lg font-semibold text-green-700 mb-2">
//                 🎉 ABHA ID Created Successfully
//             </h3>
//             <p className="text-sm text-gray-600 mb-4">Review the ABHA details before proceeding.</p>

//             <div className="flex gap-4">
//                 <img
//                     src={`data:image/jpeg;base64,${profile.photo}`}
//                     alt="ABHA Profile"
//                     className="w-20 h-20 rounded-full border border-gray-300 object-cover"
//                 />

//                 <div className="flex-1 space-y-1 text-sm text-gray-700">
//                     <div><strong>Name:</strong> {profile.firstName} {profile.middleName} {profile.lastName}</div>
//                     <div><strong>DOB:</strong> {profile.dob}</div>
//                     <div><strong>Mobile:</strong> {profile.mobile}</div>
//                     <div><strong>Address:</strong> {profile.address}, {profile.districtName}, {profile.stateName} - {profile.pinCode}</div>
//                     <div><strong>ABHA:</strong> <span className="font-mono text-sm bg-white px-2 py-1 rounded">{profile.ABHANumber}</span></div>
//                     <div><strong>PHR Address:</strong> <span className="font-mono text-sm bg-white px-2 py-1 rounded">{profile.phrAddress?.[0]}</span></div>
//                 </div>
//             </div>
//         </div>
//     );
// };

// export default AbhaProfileCard;

import React from "react";

type Props = {
    profile: any;
    isNew: boolean;
};

const AbhaProfileCard: React.FC<Props> = ({ profile, isNew }) => {
    return (
        <div className="bg-gray-100 border border-gray-300 rounded-xl p-5 shadow-inner">
            <h3 className="text-lg font-semibold text-green-700 mb-2">
                {isNew ? "🎉 ABHA ID Created Successfully" : "✅ User Already Has ABHA Account"}
            </h3>
            <p className="text-sm text-gray-600 mb-4">
                {isNew
                    ? "Review the newly created ABHA profile."
                    : "ABHA profile already exists. Review the details."}
            </p>

            <div className="flex gap-4">
                <img
                    src={profile.photo ? `data:image/jpeg;base64,${profile.photo}` : "/default-avatar.png"}
                    alt="ABHA Profile"
                    className="w-20 h-20 rounded-full border border-gray-300 object-cover"
                />

                <div className="flex-1 space-y-1 text-sm text-gray-700">
                    <div><strong>Name:</strong> {profile.firstName} {profile.middleName} {profile.lastName}</div>
                    <div><strong>DOB:</strong> {profile.dob}</div>
                    <div><strong>Mobile:</strong> {profile.mobile}</div>
                    <div><strong>Address:</strong> {profile.address}, {profile.districtName}, {profile.stateName} - {profile.pinCode}</div>
                    <div><strong>ABHA:</strong> <span className="font-mono text-sm bg-white px-2 py-1 rounded">{profile.ABHANumber}</span></div>
                    <div><strong>PHR Address:</strong> <span className="font-mono text-sm bg-white px-2 py-1 rounded">{profile.phrAddress?.[0]}</span></div>
                </div>
            </div>
        </div>
    );
};

export default AbhaProfileCard;
