// import React, { useState } from "react";
// import { enrollAbhaByOtp } from "../../services/abhaApis";
// import { showError, showSuccess } from "../../utils/toastUtils";
// import { usePatientFormStore } from "../../store/patientFormStore";
// import { mapAbhaProfileToPatient } from "../../utils/mapAbhaToPatient";
// import AbhaProfileCard from "./AbhaProfileCard"; // New pretty version

// type OtpPopupProps = {
//     txnId: string;
//     onClose: () => void;
// };

// const OTP_REGEX = /^\d{6}$/;
// const MOBILE_REGEX = /^\d{10}$/;

// const OtpPopup: React.FC<OtpPopupProps> = ({ txnId, onClose }) => {
//     const [otp, setOtp] = useState("");
//     const [mobile, setMobile] = useState("");
//     const [otpError, setOtpError] = useState("");
//     const [mobileError, setMobileError] = useState("");
//     const [loading, setLoading] = useState(false);
//     const [abhaProfile, setAbhaProfile] = useState<any>(null);
//     const [enrollmentSuccess, setEnrollmentSuccess] = useState(false);

//     const handleClose = () => {
//         setOtp("");
//         setMobile("");
//         setOtpError("");
//         setMobileError("");
//         setLoading(false);
//         setAbhaProfile(null);
//         setEnrollmentSuccess(false);
//         onClose();
//     };

//     const handleUseData = () => {
//         if (abhaProfile) {
//             const patientData = mapAbhaProfileToPatient(abhaProfile);
//             if (patientData) {
//                 usePatientFormStore.getState().setQuickFormData(patientData);
//                 showSuccess("Patient data populated successfully");
//                 handleClose();
//             } else {
//                 showError("Mapping Failed", "Could not map ABHA profile to patient format.");
//             }
//         }
//     };

//     const handleSubmit = async () => {
//         let valid = true;
//         setOtpError("");
//         setMobileError("");

//         const trimmedOtp = otp.trim();
//         const trimmedMobile = mobile.trim();

//         if (!trimmedOtp) {
//             setOtpError("OTP is required");
//             valid = false;
//         } else if (!OTP_REGEX.test(trimmedOtp)) {
//             setOtpError("OTP must be 6 digits");
//             valid = false;
//         }

//         if (!trimmedMobile) {
//             setMobileError("Mobile number is required");
//             valid = false;
//         } else if (!MOBILE_REGEX.test(trimmedMobile)) {
//             setMobileError("Mobile number must be 10 digits");
//             valid = false;
//         }

//         if (!valid || loading) return;

//         setLoading(true);
//         try {
//             const { success, data, error } = await enrollAbhaByOtp({
//                 txnId,
//                 otp: trimmedOtp,
//                 mobile: trimmedMobile,
//             });

//             if (success && data?.ABHAProfile) {
//                 setAbhaProfile(data.ABHAProfile);
//                 setEnrollmentSuccess(true);
//                 showSuccess("ABHA Enrollment Successful");
//             } else {
//                 const errorMessage = typeof error === "string"
//                     ? error
//                     : error?.details || error?.message || "Something went wrong during enrollment.";
//                 showError("Enrollment Failed", errorMessage);
//             }
//         } catch (err: any) {
//             const extractedError = err?.response?.data?.details
//                 || err?.response?.data?.message
//                 || err?.message
//                 || "Unexpected server error occurred.";
//             showError("Server Error", extractedError);
//         } finally {
//             setLoading(false);
//         }
//     };

//     return (
//         <div className="fixed inset-0 bg-black/40 backdrop-blur-sm z-50 flex items-center justify-center">
//             <div className="bg-white rounded-xl shadow-lg p-6 w-full max-w-md relative">
//                 <button
//                     className="absolute top-2 right-2 text-xl font-bold text-gray-500 hover:text-gray-800"
//                     onClick={handleClose}
//                 >
//                     &times;
//                 </button>

//                 {enrollmentSuccess && abhaProfile ? (
//                     <>
//                         <AbhaProfileCard profile={abhaProfile} />
//                         <div className="mt-4 text-right">
//                             <button
//                                 onClick={handleUseData}
//                                 className="btn btn-primary px-5 py-2 text-sm font-medium rounded bg-blue-600 text-white hover:bg-blue-700 transition"
//                             >
//                                 Use this Data
//                             </button>
//                         </div>
//                     </>
//                 ) : (
//                     <>
//                         <h2 className="text-lg font-semibold mb-5">Enter OTP & Phone Number</h2>

//                         <div className="mb-4">
//                             <label className="block text-sm font-medium mb-1">OTP</label>
//                             <input
//                                 type="tel"
//                                 value={otp}
//                                 onChange={(e) => setOtp(e.target.value.replace(/\D/g, ""))}
//                                 className="input input-bordered w-full text-sm"
//                                 placeholder="Enter OTP"
//                             />
//                             {otpError && <p className="text-red-500 text-xs mt-1">{otpError}</p>}
//                         </div>

//                         <div className="mb-4">
//                             <label className="block text-sm font-medium mb-1">Phone Number</label>
//                             <input
//                                 type="tel"
//                                 value={mobile}
//                                 onChange={(e) => setMobile(e.target.value.replace(/\D/g, ""))}
//                                 className="input input-bordered w-full text-sm"
//                                 placeholder="Enter phone number"
//                             />
//                             {mobileError && (
//                                 <p className="text-red-500 text-xs mt-1">{mobileError}</p>
//                             )}
//                         </div>

//                         <div className="flex justify-end">
//                             <button
//                                 onClick={handleSubmit}
//                                 disabled={loading}
//                                 className="btn btn-success btn-sm px-6"
//                             >
//                                 {loading ? "Submitting..." : "Submit"}
//                             </button>
//                         </div>
//                     </>
//                 )}
//             </div>
//         </div>
//     );
// };

// export default OtpPopup;

import React, { useState } from "react";
import { enrollAbhaByOtp } from "../../services/abhaApis";
import { showError, showSuccess } from "../../utils/toastUtils";
import { usePatientFormStore } from "../../store/patientFormStore";
import { mapAbhaProfileToPatient } from "../../utils/mapAbhaToPatient";
import AbhaProfileCard from "./AbhaProfileCard";

type OtpPopupProps = {
    txnId: string;
    onClose: () => void;
};

const OTP_REGEX = /^\d{6}$/;
const MOBILE_REGEX = /^\d{10}$/;

const OtpPopup: React.FC<OtpPopupProps> = ({ txnId, onClose }) => {
    const [otp, setOtp] = useState("");
    const [mobile, setMobile] = useState("");
    const [otpError, setOtpError] = useState("");
    const [mobileError, setMobileError] = useState("");
    const [loading, setLoading] = useState(false);
    const [abhaProfile, setAbhaProfile] = useState<any>(null);
    const [isNew, setIsNew] = useState<boolean>(true); // NEW
    const [enrollmentSuccess, setEnrollmentSuccess] = useState(false);

    const handleClose = () => {
        setOtp("");
        setMobile("");
        setOtpError("");
        setMobileError("");
        setLoading(false);
        setAbhaProfile(null);
        setEnrollmentSuccess(false);
        setIsNew(true);
        onClose();
    };

    const handleUseData = () => {
        if (abhaProfile) {
            const patientData = mapAbhaProfileToPatient(abhaProfile);
            if (patientData) {
                usePatientFormStore.getState().setQuickFormData(patientData);
                showSuccess("Patient data populated successfully");
                handleClose();
            } else {
                showError("Mapping Failed", "Could not map ABHA profile to patient format.");
            }
        }
    };

    const handleSubmit = async () => {
        let valid = true;
        setOtpError("");
        setMobileError("");

        const trimmedOtp = otp.trim();
        const trimmedMobile = mobile.trim();

        if (!trimmedOtp) {
            setOtpError("OTP is required");
            valid = false;
        } else if (!OTP_REGEX.test(trimmedOtp)) {
            setOtpError("OTP must be 6 digits");
            valid = false;
        }

        if (!trimmedMobile) {
            setMobileError("Mobile number is required");
            valid = false;
        } else if (!MOBILE_REGEX.test(trimmedMobile)) {
            setMobileError("Mobile number must be 10 digits");
            valid = false;
        }

        if (!valid || loading) return;

        setLoading(true);
        try {
            const { success, data, error } = await enrollAbhaByOtp({
                txnId,
                otp: trimmedOtp,
                mobile: trimmedMobile,
            });

            if (success && data?.ABHAProfile) {
                setAbhaProfile(data.ABHAProfile);
                setIsNew(data.isNew ?? true); // <-- updated from response
                setEnrollmentSuccess(true);
                showSuccess("ABHA Enrollment Successful");
            } else {
                const errorMessage = typeof error === "string"
                    ? error
                    : error?.details || error?.message || "Something went wrong during enrollment.";
                showError("Enrollment Failed", errorMessage);
            }
        } catch (err: any) {
            const extractedError = err?.response?.data?.details
                || err?.response?.data?.message
                || err?.message
                || "Unexpected server error occurred.";
            showError("Server Error", extractedError);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="fixed inset-0 bg-black/40 backdrop-blur-sm z-50 flex items-center justify-center">
            <div className="bg-white rounded-xl shadow-lg p-6 w-full max-w-md relative">
                <button
                    className="absolute top-2 right-2 text-xl font-bold text-gray-500 hover:text-gray-800"
                    onClick={handleClose}
                >
                    &times;
                </button>

                {enrollmentSuccess && abhaProfile ? (
                    <>
                        <AbhaProfileCard profile={abhaProfile} isNew={isNew} />
                        <div className="mt-4 text-right">
                            <button
                                onClick={handleUseData}
                                className="px-5 py-2 text-sm font-medium rounded bg-blue-600 text-white hover:bg-blue-700 transition"
                            >
                                Use this Data
                            </button>
                        </div>
                    </>
                ) : (
                    <>
                        <h2 className="text-lg font-semibold mb-5">Enter OTP & Phone Number</h2>

                        <div className="mb-4">
                            <label className="block text-sm font-medium mb-1">OTP</label>
                            <input
                                type="tel"
                                value={otp}
                                onChange={(e) => setOtp(e.target.value.replace(/\D/g, ""))}
                                className="input input-bordered w-full text-sm"
                                placeholder="Enter OTP"
                            />
                            {otpError && <p className="text-red-500 text-xs mt-1">{otpError}</p>}
                        </div>

                        <div className="mb-4">
                            <label className="block text-sm font-medium mb-1">Phone Number</label>
                            <input
                                type="tel"
                                value={mobile}
                                onChange={(e) => setMobile(e.target.value.replace(/\D/g, ""))}
                                className="input input-bordered w-full text-sm"
                                placeholder="Enter phone number"
                            />
                            {mobileError && (
                                <p className="text-red-500 text-xs mt-1">{mobileError}</p>
                            )}
                        </div>

                        <div className="flex justify-end">
                            <button
                                onClick={handleSubmit}
                                disabled={loading}
                                className="btn btn-success btn-sm px-6"
                            >
                                {loading ? "Submitting..." : "Submit"}
                            </button>
                        </div>
                    </>
                )}
            </div>
        </div>
    );
};

export default OtpPopup;

