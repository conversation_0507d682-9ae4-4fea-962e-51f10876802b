import React from "react";

type Props = {
  panNumber: string;
  setPanNumber: (value: string) => void;
};

const PanInput: React.FC<Props> = ({ panNumber, setPanNumber }) => {
  return (
    <input
      type="text"
      maxLength={10}
      value={panNumber}
      onChange={(e) => setPanNumber(e.target.value.toUpperCase())}
      placeholder="Enter PAN Number"
      className="border rounded-md px-3 py-1.5 text-sm shadow-sm "
    />
  );
};

export default PanInput;
