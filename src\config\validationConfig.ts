// import type{ ValidationMessages } from '../types/validation';


// // Default validation messages - can be overridden per form or globally
// export const defaultValidationConfig: ValidationMessages = {
//   required: {
//     facility: "Please select a facility",
//     title: "Please select a title",
//     firstName: "First name is required",
//     lastName: "Last name is required",
//     dateOfBirth: "Date of birth is required",
//     gender: "Please select gender",
//     identifierType: "Please select identifier type",
//     identifierNumber: "Identifier number is required",
//     primaryPhone: "Primary phone number is required",
//   },
//   format: {
//     name: "Only letters and spaces are allowed",
//     phone: "Phone number must be exactly 10 digits",
//     email: "Please enter a valid email address",
//     pincode: "Pinco<PERSON> must be 6 digits",
//     identifier: "Identifier must be 6-20 alphanumeric characters",
//   },
//   custom: {
//     phoneRequired: "Phone number is required and must be 10 digits",
//     pincodeExact: "<PERSON>nco<PERSON> must be exactly 6 digits",
//     contactNameLetters: "Contact name must contain only letters and spaces",
//     phoneExact: "Phone number must be exactly 10 digits",
//   }
// };

// // Friendly/User-friendly validation messages
// export const friendlyValidationConfig: ValidationMessages = {
//   required: {
//     facility: "Oops! Please choose a facility to continue",
//     title: "Don't forget to select your title",
//     firstName: "We need your first name",
//     lastName: "Your last name is required",
//     dateOfBirth: "Please tell us your date of birth",
//     gender: "Please let us know your gender",
//     identifierType: "Please choose an ID type",
//     identifierNumber: "Your ID number is needed",
//     primaryPhone: "We need a phone number to reach you",
//   },
//   format: {
//     name: "Names can only contain letters and spaces",
//     phone: "Phone numbers should be exactly 10 digits",
//     email: "That doesn't look like a valid email address",
//     pincode: "Pincode should be 6 digits",
//     identifier: "ID should be 6-20 characters with letters and numbers",
//   },
//   custom: {
//     phoneRequired: "We need a valid 10-digit phone number",
//     pincodeExact: "Please enter a valid 6-digit pincode",
//     contactNameLetters: "Contact names should only have letters and spaces",
//     phoneExact: "Please enter exactly 10 digits for the phone number",
//     aadhaarFormat: "Aadhaar number should be exactly 12 digits",
//     abhaFormat: "ABHA number should be exactly 14 digits",
//     panFormat: "PAN should be in format: **********",
//     passportFormat: "Passport should be in format: ********",
//     drivingLicenseFormat: "Driving License should be in format: AB1234567890123",
//   }
// };

// // Professional/Medical validation messages
// export const medicalValidationConfig: ValidationMessages = {
//   required: {
//     facility: "Healthcare facility selection is mandatory",
//     title: "Patient title must be specified",
//     firstName: "Patient's first name is required",
//     lastName: "Patient's last name is required",
//     dateOfBirth: "Date of birth is mandatory for patient records",
//     gender: "Gender information is required",
//     identifierType: "Patient identifier type must be selected",
//     identifierNumber: "Patient identifier number is mandatory",
//     primaryPhone: "Primary contact number is required for patient communication",
//   },
//   format: {
//     name: "Names must contain only alphabetic characters and spaces",
//     phone: "Contact number must be a valid 10-digit number",
//     email: "Email address format is invalid",
//     pincode: "Postal code must be exactly 6 digits",
//     identifier: "Patient identifier must be 6-20 alphanumeric characters",
//   },
//   custom: {
//     phoneRequired: "Valid 10-digit contact number is mandatory",
//     pincodeExact: "Postal code must be exactly 6 digits",
//     contactNameLetters: "Emergency contact name must contain only letters and spaces",
//     phoneExact: "Contact number must be exactly 10 digits",
//   }
// };

// // Multilingual support - Hindi messages (example)
// export const hindiValidationConfig: ValidationMessages = {
//   required: {
//     facility: "कृपया एक सुविधा चुनें",
//     title: "कृपया शीर्षक चुनें",
//     firstName: "पहला नाम आवश्यक है",
//     lastName: "अंतिम नाम आवश्यक है",
//     dateOfBirth: "जन्म तिथि आवश्यक है",
//     gender: "कृपया लिंग चुनें",
//     identifierType: "कृपया पहचानकर्ता प्रकार चुनें",
//     identifierNumber: "पहचानकर्ता संख्या आवश्यक है",
//     primaryPhone: "प्राथमिक फोन नंबर आवश्यक है",
//   },
//   format: {
//     name: "केवल अक्षर और स्थान की अनुमति है",
//     phone: "फोन नंबर 10 अंकों का होना चाहिए",
//     email: "कृपया वैध ईमेल पता दर्ज करें",
//     pincode: "पिनकोड 6 अंकों का होना चाहिए",
//     identifier: "पहचानकर्ता 6-20 अक्षरांकीय वर्ण होना चाहिए",
//   },
//   custom: {
//     phoneRequired: "फोन नंबर आवश्यक है और 10 अंकों का होना चाहिए",
//     pincodeExact: "पिनकोड बिल्कुल 6 अंकों का होना चाहिए",
//     contactNameLetters: "संपर्क नाम में केवल अक्षर और स्थान होना चाहिए",
//     phoneExact: "फोन नंबर बिल्कुल 10 अंकों का होना चाहिए",
//   }
// };

// // Validation configuration presets
// export const validationPresets = {
//   default: defaultValidationConfig,
//   friendly: friendlyValidationConfig,
//   medical: medicalValidationConfig,
//   hindi: hindiValidationConfig,
// };

// // Function to get validation config by preset name
// export const getValidationConfig = (preset: keyof typeof validationPresets = 'default'): ValidationMessages => {
//   return validationPresets[preset];
// };

// // Function to merge custom messages with a preset
// export const createCustomValidationConfig = (
//   preset: keyof typeof validationPresets = 'default',
//   overrides: Partial<ValidationMessages> = {}
// ): ValidationMessages => {
//   const baseConfig = getValidationConfig(preset);

//   return {
//     required: { ...baseConfig.required, ...overrides.required },
//     format: { ...baseConfig.format, ...overrides.format },
//     custom: { ...baseConfig.custom, ...overrides.custom },
//   };
// };

// // Real-time validation settings
// export interface ValidationSettings {
//   validateOnChange: boolean;
//   validateOnBlur: boolean;
//   showErrorIcons: boolean;
//   debounceMs: number;
//   preset: keyof typeof validationPresets;
// }

// export const defaultValidationSettings: ValidationSettings = {
//   validateOnChange: true,
//   validateOnBlur: true,
//   showErrorIcons: true,
//   debounceMs: 300,
//   preset: 'default'
// };

// // Field-specific validation settings
// export interface FieldValidationConfig {
//   [fieldName: string]: {
//     validateOnChange?: boolean;
//     validateOnBlur?: boolean;
//     customMessage?: string;
//     helpText?: string;
//     required?: boolean;
//   };
// }

// export const patientFormFieldConfig: FieldValidationConfig = {
//   firstName: {
//     validateOnChange: true,
//     validateOnBlur: true,
//     helpText: "Enter patient's first name",
//     required: true
//   },
//   lastName: {
//     validateOnChange: true,
//     validateOnBlur: true,
//     helpText: "Enter patient's last name",
//     required: true
//   },
//   dateOfBirth: {
//     validateOnBlur: true,
//     helpText: "Select patient's date of birth",
//     required: true
//   },
//   'contacts.0.phoneNumber': {
//     validateOnChange: true,
//     validateOnBlur: true,
//     helpText: "Primary contact number (10 digits)",
//     required: true
//   },
//   email: {
//     validateOnBlur: true,
//     helpText: "Enter a valid email address (optional)"
//   },
//   pincode: {
//     validateOnChange: true,
//     helpText: "6-digit postal code"
//   }
// };
