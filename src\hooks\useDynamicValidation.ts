import { useState, useCallback } from 'react';
import { ZodSchema, ZodError } from 'zod';

type ValidationErrors = Record<string, string>;

export const useDynamicValidation = <T>(schema: ZodSchema<T>) => {
  const [errors, setErrors] = useState<ValidationErrors>({});

  // Validate a single field dynamically
  const validateField = useCallback((fieldPath: string, value: any, formData: T) => {
    try {
      // Create a partial object with just this field for validation
      const fieldValue = getNestedValue(formData, fieldPath);

      // Try to validate the entire form to get cross-field validation
      schema.parse(formData);

      // If validation passes, clear the error for this field
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldPath];
        return newErrors;
      });
    } catch (error) {
      if (error instanceof ZodError) {
        const fieldErrors: ValidationErrors = {};

        error.errors.forEach((e) => {
          const path = e.path.join(".");
          fieldErrors[path] = e.message;
        });

        setErrors(prev => ({
          ...prev,
          ...fieldErrors
        }));
      }
    }
  }, [schema]);

  // Validate specific field and clear error if valid
  const validateSingleField = useCallback((fieldPath: string, value: any, formData: T) => {
    try {
      // For simple field validation, we'll use a more targeted approach
      const fieldSchema = getFieldSchema(schema, fieldPath);
      if (fieldSchema) {
        fieldSchema.parse(value);
        // If validation passes, clear the error
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[fieldPath];
          return newErrors;
        });
      }
    } catch (error) {
      if (error instanceof ZodError) {
        setErrors(prev => ({
          ...prev,
          [fieldPath]: error.errors[0]?.message || 'Invalid value'
        }));
      }
    }
  }, [schema]);

  // Clear specific field error
  const clearFieldError = useCallback((fieldPath: string) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[fieldPath];
      return newErrors;
    });
  }, []);

  // Set errors from external validation (like form submission)
  const setValidationErrors = useCallback((validationErrors: ValidationErrors) => {
    setErrors(validationErrors);
  }, []);

  // Clear all errors
  const clearAllErrors = useCallback(() => {
    setErrors({});
  }, []);

  // Validate entire form and return errors
  const validateForm = useCallback((formData: T): ValidationErrors => {
    try {
      schema.parse(formData);
      setErrors({});
      return {};
    } catch (error) {
      if (error instanceof ZodError) {
        const fieldErrors: ValidationErrors = {};
        error.errors.forEach((e) => {
          const path = e.path.join(".");
          fieldErrors[path] = e.message;
        });
        setErrors(fieldErrors);
        return fieldErrors;
      }
      return {};
    }
  }, [schema]);

  return {
    errors,
    validateField,
    validateSingleField,
    clearFieldError,
    setValidationErrors,
    clearAllErrors,
    validateForm
  };
};

// Helper function to get nested value from object
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => {
    if (current && typeof current === 'object') {
      // Handle array indices
      if (!isNaN(Number(key))) {
        return current[Number(key)];
      }
      return current[key];
    }
    return undefined;
  }, obj);
}

// Helper function to get field schema (simplified approach)
function getFieldSchema(schema: ZodSchema<any>, fieldPath: string): ZodSchema<any> | null {
  // This is a simplified approach - for complex validation,
  // we'll rely on the full form validation
  return null;
}

// Enhanced hook specifically for patient form with custom validation logic
export const usePatientDynamicValidation = () => {
  const [errors, setErrors] = useState<ValidationErrors>({});

  // Dynamic validation for specific patient form fields
  const validatePatientField = useCallback((fieldName: string, value: any, formData: any) => {
    const newErrors: ValidationErrors = {};

    // Debug logging
    console.log('🔍 Validating field:', fieldName, 'with value:', value, 'formData keys:', Object.keys(formData || {}));


     // 1. Phone/Mobile number validation (handle contacts.n.phoneNumber or mobileNumber)
  const phoneMobileMatch = fieldName.match(/^contacts\.(\d+)\.(phoneNumber|mobileNumber)$/);
  if (phoneMobileMatch) {
    const isPrimary = fieldName === 'contacts.0.phoneNumber';

    if (!value || value.trim() === '') {
      newErrors[fieldName] = isPrimary
        ? 'Primary contact phone number is required'
        : 'Phone number is required';
    } else {
      // Remove spaces, dashes, parentheses
      const cleanValue = value.replace(/[-\s()]/g, '');
      if (!/^\d{10}$/.test(cleanValue)) {
        newErrors[fieldName] = 'Phone number must be 10 digits';
      }
    }

    // Update errors and return early
    setErrors(prev => {
      const updated = { ...prev };
      delete updated[fieldName];

      if (Object.keys(newErrors).length > 0) {
        Object.assign(updated, newErrors);
      }

      return updated;
    });

    return Object.keys(newErrors).length === 0;
  }

    

    switch (fieldName) {
      case 'firstName':
      case 'lastName':
        console.log(`🔍 Validating ${fieldName}:`, { value, trimmed: value?.trim(), length: value?.trim()?.length });
        if (!value || value.trim().length === 0) {
          newErrors[fieldName] = `${fieldName === 'firstName' ? 'First' : 'Last'} name is required`;
          console.log(`❌ ${fieldName} is empty, setting required error`);
        } else if (!/^[A-Za-z\s]+$/.test(value)) {
          newErrors[fieldName] = 'Only letters and spaces allowed';
          console.log(`❌ ${fieldName} has invalid characters`);
        } else if (value.length > 20) {
          newErrors[fieldName] = 'Name must not exceed 20 characters';
          console.log(`❌ ${fieldName} exceeds 20 characters`);
        } else {
          console.log(`✅ ${fieldName} is valid`);
        }
        break;

      case 'middleName':
        if (value && !/^[A-Za-z\s]+$/.test(value)) {
          newErrors[fieldName] = 'Only letters and spaces allowed';
        } else if (value && value.length > 20) {
          newErrors[fieldName] = 'Name must not exceed 20 characters';
        }
        break;

      case 'dateOfBirth':
        if (!value || value.trim().length === 0) {
          newErrors[fieldName] = 'Date of birth is required';
        } else {
          const date = new Date(value);
          const today = new Date();
          today.setHours(23, 59, 59, 999); // Set to end of today
          if (date > today) {
            newErrors[fieldName] = 'Date of birth cannot be in the future';
          }
        }
        break;

      case 'age':
        if (value !== null && value !== undefined && value !== '') {
          const ageNum = typeof value === 'string' ? parseInt(value, 10) : value;
          if (isNaN(ageNum) || ageNum < 0 || ageNum > 120) {
            newErrors[fieldName] = 'Age must be between 0 and 120 years';
          }
        }
        break;

      case 'title':
        if (!value || value === '') {
          newErrors[fieldName] = 'Title is required';
        }
        break;



      case 'gender':
        if (!value || value === '') {
          newErrors[fieldName] = 'Gender is required';
        }
        break;

      case 'facilityId':
        if (!value || value === '') {
          newErrors[fieldName] = 'Facility is required';
        }
        break;

      case 'identifierType':
        if ((!value || value === '') && formData.identifierNumber) {
          newErrors[fieldName] = 'Please select Identifier Type before entering Identifier Number';
        }
        break;

      case 'identifierNumber':
        if (value && (!formData.identifierType || formData.identifierType === '')) {
          newErrors['identifierType'] = 'Please select Identifier Type before entering Identifier Number';
        } else if (value && formData.identifierType) {
          // Validate based on identifier type
          const patterns: Record<string, { regex: RegExp; message: string }> = {
            'ABHA': {
              regex: /^\d{2}-\d{4}-\d{4}-\d{4}$/,
              message: 'ABHA must be in the format 12-1234-1234-1234',
            },
            'Aadhar': {
              regex: /^(\d{12}|\d{4}\s\d{4}\s\d{4})$/,
              message: 'Aadhaar must be 12 digits or in the format 1234 5678 9012',
            },
            'PAN': {
              regex: /^[A-Z]{5}[0-9]{4}[A-Z]$/,
              message: 'PAN must be in the format **********',
            },
            'Passport': {
              regex: /^[A-Z][0-9]{7}$/,
              message: 'Passport must be in the format ********',
            },
            'Driving_License': {
              regex: /^[A-Z]{2}[0-9]{2}[0-9]{11}$/,
              message: 'Driving License must be in the format TS11620210003606',
            },
          };

          const pattern = patterns[formData.identifierType];
          if (pattern && !pattern.regex.test(value)) {
            newErrors[fieldName] = pattern.message;
          }
        }
        break;
        
        

      // Contact validation
      // case 'contacts.0.phoneNumber':
      // case 'phoneNumber': { // Handle both field names
      //   console.log('🔍 Phone validation:', { fieldName, value, formDataPhone: formData.contacts?.[0]?.phoneNumber });

      //   // Skip validation if this is the initial empty state
      //   const isInitialEmptyState = (value === null || value === undefined || value === '') &&
      //                              (formData.contacts?.[0]?.phoneNumber === null || formData.contacts?.[0]?.phoneNumber === undefined || formData.contacts?.[0]?.phoneNumber === '');

      //   if (isInitialEmptyState) {
      //     console.log('📝 Skipping validation for initial empty state');
      //     break;
      //   }

      //   if (!value || value.trim() === '') {
      //     newErrors[fieldName] = 'Primary contact phone number is required';
      //   } else {
      //     // Clean the value and check if it's exactly 10 digits
      //     const cleanValue = value.replace(/[-\s()]/g, '');
      //     if (!/^\d{10}$/.test(cleanValue)) {
      //       newErrors[fieldName] = 'Phone number must be 10 digits';
      //     }
      //     // If validation passes, the error will be cleared below
      //   }
      //   break;
      // }

      // Email validation
      case 'contacts.0.email':
      case 'email': // Handle both field names
        if (value && !/^[\w-.]+@([\w-]+\.)+[\w-]{2,4}$/.test(value)) {
          newErrors[fieldName] = 'Invalid email format';
        }
        // If validation passes, the error will be cleared below
        break;

      // Address validation
      default:
        if (fieldName.includes('pincode') && value && !/^\d{6}$/.test(value)) {
          newErrors[fieldName] = 'Pincode must be exactly 6 digits';
        }

        // Emergency contact validation
        if (fieldName.includes('emergencyContacts') && fieldName.includes('contactName')) {
          if (value && !/^[A-Za-z\s]+$/.test(value)) {
            newErrors[fieldName] = 'Contact name must contain only letters';
          }
        }

        if (fieldName.includes('emergencyContacts') && fieldName.includes('phoneNumber')) {
          if (value && !/^\d{10}$/.test(value.replace(/[-\s()]/g, ''))) {
            newErrors[fieldName] = 'Phone number must be exactly 10 digits';
          }
        }

        // ABHA fields validation
        if (fieldName.includes('abha') && fieldName.includes('abhaNumber')) {
          if (value && !/^\d{2}-\d{4}-\d{4}-\d{4}$/.test(value)) {
            newErrors[fieldName] = 'ABHA must be in the format 12-1234-1234-1234';
          }
        }

        // Policy date validation
        if (fieldName.includes('insurance')) {
          if (fieldName.includes('policyStartDate') && value) {
            // Check if end date exists and validate relationship
            const endDate = formData.insurance?.policyEndDate;
            if (endDate) {
              const startDate = new Date(value);
              const endDateObj = new Date(endDate);
              if (endDateObj <= startDate) {
                newErrors['insurance.policyEndDate'] = 'Policy end date must be greater than start date';
              }
            }
          }

          if (fieldName.includes('policyEndDate') && value) {
            // Check if start date exists and validate relationship
            const startDate = formData.insurance?.policyStartDate;
            if (startDate) {
              const startDateObj = new Date(startDate);
              const endDate = new Date(value);
              if (endDate <= startDateObj) {
                newErrors[fieldName] = 'Policy end date must be greater than start date';
              }
            }
          }
        }

        break;
    }

    // Update errors state
    setErrors(prev => {
      const updated = { ...prev };

      // Always remove the current field's error first
      delete updated[fieldName];

      // Remove error if field is now valid (no new errors for this field)
      if (Object.keys(newErrors).length === 0) {
        console.log('✅ Clearing error for field:', fieldName);
        // Also clear related errors
        if (fieldName === 'identifierType') {
          delete updated['identifierNumber'];
        }
        if (fieldName === 'identifierNumber') {
          delete updated['identifierType'];
        }
      } else {
        console.log('❌ Setting error for field:', fieldName, 'errors:', newErrors);
        // Add new errors
        Object.assign(updated, newErrors);
      }

      console.log('📊 Updated errors state:', updated);
      return updated;
    });

    return Object.keys(newErrors).length === 0;
  }, []);

  // Clear specific field error
  const clearFieldError = useCallback((fieldPath: string) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[fieldPath];
      return newErrors;
    });
  }, []);

  // Set errors from external validation (like form submission)
  const setValidationErrors = useCallback((validationErrors: ValidationErrors) => {
    setErrors(validationErrors);
  }, []);

  // Clear all errors
  const clearAllErrors = useCallback(() => {
    setErrors({});
  }, []);

  // Enhanced validation function that handles async state updates
  const validateFieldWithDelay = useCallback((fieldName: string, value: any, formData: any) => {
    // Use setTimeout to ensure React state has been updated
    setTimeout(() => {
      validatePatientField(fieldName, value, formData);
    }, 0);
  }, [validatePatientField]);

  return {
    errors,
    validatePatientField,
    validateFieldWithDelay,
    clearFieldError,
    setValidationErrors,
    clearAllErrors
  };
};
