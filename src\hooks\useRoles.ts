// src/hooks/useRoles.ts
import { useMemo } from "react";

function decodeToken(token: string) {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => `%${('00' + c.charCodeAt(0).toString(16)).slice(-2)}`)
        .join('')
    );
    return JSON.parse(jsonPayload);
  } catch (err) {
    console.error("Failed to decode token", err);
    return null;
  }
}

export const useRoles = () => {
  const token = localStorage.getItem("access_token");

  const allRoles: string[] = useMemo(() => {
    if (!token) return [];

    const decoded = decodeToken(token);
    const realmRoles = decoded?.realm_access?.roles || [];
    const clientRoles = decoded?.resource_access?.["react-client"]?.roles || [];

    return [...realmRoles, ...clientRoles];
  }, [token]);

  const hasRole = (role: string) => {
    return allRoles.includes(role);
  };

  return { allRoles, hasRole };
};
