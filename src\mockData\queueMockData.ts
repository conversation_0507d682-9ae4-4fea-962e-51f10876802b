import { 
  type QueueEntry, 
  type QueueSummary, 
  type QueueStatistics, 
  type WaitTimeEstimation,
  QueueStatus,
  ServiceType 
} from '../types/queue';
import { AppointmentPriority } from '../types/appointmentenums';

// Mock Queue Entries
export const mockQueueEntries: QueueEntry[] = [
  {
    queueId: "q-001",
    appointmentId: "apt-001",
    patientId: "pat-001",
    patientName: "John Doe",
    queueNumber: 1,
    serviceType: ServiceType.Consultation,
    priority: AppointmentPriority.Normal,
    status: QueueStatus.InService,
    estimatedWaitTime: 0,
    estimatedServiceTime: "10:30",
    actualWaitTime: 15,
    joinedAt: "2024-01-15T09:45:00Z",
    calledAt: "2024-01-15T10:00:00Z",
    serviceStartedAt: "2024-01-15T10:15:00Z",
    facilityId: "fac-001",
    providerId: "prov-001",
    patient: {
      firstName: "<PERSON>",
      lastName: "<PERSON><PERSON>",
      mobileNumber: "+91-**********",
      age: 35,
      gender: "Male"
    },
    provider: {
      firstName: "Dr. <PERSON>",
      lastName: "<PERSON>",
      title: "Dr.",
      specialization: "Cardiology"
    }
  },
  {
    queueId: "q-002",
    appointmentId: "apt-002",
    patientId: "pat-002",
    patientName: "Jane Smith",
    queueNumber: 2,
    serviceType: ServiceType.Consultation,
    priority: AppointmentPriority.High,
    status: QueueStatus.Called,
    estimatedWaitTime: 5,
    estimatedServiceTime: "10:45",
    joinedAt: "2024-01-15T09:50:00Z",
    calledAt: "2024-01-15T10:25:00Z",
    facilityId: "fac-001",
    providerId: "prov-001",
    patient: {
      firstName: "Jane",
      lastName: "Smith",
      mobileNumber: "+91-**********",
      age: 28,
      gender: "Female"
    },
    provider: {
      firstName: "Dr. Sarah",
      lastName: "Smith",
      title: "Dr.",
      specialization: "Cardiology"
    }
  },
  {
    queueId: "q-003",
    appointmentId: "apt-003",
    patientId: "pat-003",
    patientName: "Robert Johnson",
    queueNumber: 3,
    serviceType: ServiceType.Consultation,
    priority: AppointmentPriority.Normal,
    status: QueueStatus.Waiting,
    estimatedWaitTime: 25,
    estimatedServiceTime: "11:00",
    joinedAt: "2024-01-15T09:55:00Z",
    facilityId: "fac-001",
    providerId: "prov-001",
    patient: {
      firstName: "Robert",
      lastName: "Johnson",
      mobileNumber: "+91-**********",
      age: 42,
      gender: "Male"
    },
    provider: {
      firstName: "Dr. Sarah",
      lastName: "Smith",
      title: "Dr.",
      specialization: "Cardiology"
    }
  },
  {
    queueId: "q-004",
    patientId: "pat-004",
    patientName: "Emergency Patient",
    queueNumber: 1,
    serviceType: ServiceType.Emergency,
    priority: AppointmentPriority.Emergency,
    status: QueueStatus.Waiting,
    estimatedWaitTime: 2,
    estimatedServiceTime: "10:32",
    joinedAt: "2024-01-15T10:30:00Z",
    facilityId: "fac-001",
    patient: {
      firstName: "Emergency",
      lastName: "Patient",
      mobileNumber: "+91-**********",
      age: 55,
      gender: "Male"
    }
  },
  {
    queueId: "q-005",
    appointmentId: "apt-005",
    patientId: "pat-005",
    patientName: "Maria Garcia",
    queueNumber: 4,
    serviceType: ServiceType.Consultation,
    priority: AppointmentPriority.Normal,
    status: QueueStatus.Waiting,
    estimatedWaitTime: 40,
    estimatedServiceTime: "11:15",
    joinedAt: "2024-01-15T10:00:00Z",
    facilityId: "fac-001",
    providerId: "prov-001",
    patient: {
      firstName: "Maria",
      lastName: "Garcia",
      mobileNumber: "+91-**********",
      age: 31,
      gender: "Female"
    },
    provider: {
      firstName: "Dr. Sarah",
      lastName: "Smith",
      title: "Dr.",
      specialization: "Cardiology"
    }
  },
  {
    queueId: "q-006",
    patientId: "pat-006",
    patientName: "David Wilson",
    queueNumber: 1,
    serviceType: ServiceType.Laboratory,
    priority: AppointmentPriority.Normal,
    status: QueueStatus.Waiting,
    estimatedWaitTime: 10,
    estimatedServiceTime: "10:40",
    joinedAt: "2024-01-15T10:20:00Z",
    facilityId: "fac-001",
    patient: {
      firstName: "David",
      lastName: "Wilson",
      mobileNumber: "+91-**********",
      age: 38,
      gender: "Male"
    }
  },
  {
    queueId: "q-007",
    patientId: "pat-007",
    patientName: "Lisa Brown",
    queueNumber: 2,
    serviceType: ServiceType.Laboratory,
    priority: AppointmentPriority.Normal,
    status: QueueStatus.Waiting,
    estimatedWaitTime: 20,
    estimatedServiceTime: "10:50",
    joinedAt: "2024-01-15T10:25:00Z",
    facilityId: "fac-001",
    patient: {
      firstName: "Lisa",
      lastName: "Brown",
      mobileNumber: "+91-**********",
      age: 29,
      gender: "Female"
    }
  }
];

// Mock Queue Summary
export const mockQueueSummary: QueueSummary = {
  serviceType: ServiceType.Consultation,
  facilityId: "fac-001",
  date: "2024-01-15",
  totalInQueue: 4,
  currentlyServing: {
    queueEntry: mockQueueEntries[0],
    serviceStartTime: "10:15"
  },
  queue: mockQueueEntries.filter(entry => 
    entry.serviceType === ServiceType.Consultation && 
    entry.status !== QueueStatus.Completed
  ),
  averageServiceTime: 18,
  estimatedWaitTime: 25,
  peakHours: ["10:00-11:00", "14:00-15:00", "16:00-17:00"],
  lastUpdated: "2024-01-15T10:30:00Z"
};

// Mock Wait Time Estimations
export const mockWaitTimeEstimations: { [key in ServiceType]?: WaitTimeEstimation } = {
  [ServiceType.Consultation]: {
    serviceType: ServiceType.Consultation,
    facilityId: "fac-001",
    estimatedWaitTime: 25,
    queuePosition: 3,
    patientsAhead: 2,
    averageServiceTime: 18,
    currentWaitTime: {
      [AppointmentPriority.Low]: 45,
      [AppointmentPriority.Normal]: 25,
      [AppointmentPriority.High]: 15,
      [AppointmentPriority.Urgent]: 8,
      [AppointmentPriority.Emergency]: 2
    },
    lastUpdated: "2024-01-15T10:30:00Z",
    confidence: 85
  },
  [ServiceType.Emergency]: {
    serviceType: ServiceType.Emergency,
    facilityId: "fac-001",
    estimatedWaitTime: 2,
    queuePosition: 1,
    patientsAhead: 0,
    averageServiceTime: 30,
    currentWaitTime: {
      [AppointmentPriority.Low]: 60,
      [AppointmentPriority.Normal]: 45,
      [AppointmentPriority.High]: 30,
      [AppointmentPriority.Urgent]: 15,
      [AppointmentPriority.Emergency]: 2
    },
    lastUpdated: "2024-01-15T10:30:00Z",
    confidence: 95
  },
  [ServiceType.Laboratory]: {
    serviceType: ServiceType.Laboratory,
    facilityId: "fac-001",
    estimatedWaitTime: 15,
    queuePosition: 2,
    patientsAhead: 1,
    averageServiceTime: 10,
    currentWaitTime: {
      [AppointmentPriority.Low]: 25,
      [AppointmentPriority.Normal]: 15,
      [AppointmentPriority.High]: 10,
      [AppointmentPriority.Urgent]: 5,
      [AppointmentPriority.Emergency]: 1
    },
    lastUpdated: "2024-01-15T10:30:00Z",
    confidence: 90
  }
};

// Mock Queue Statistics
export const mockQueueStatistics: QueueStatistics = {
  facilityId: "fac-001",
  date: "2024-01-15",
  totalProcessed: 45,
  currentInQueue: 12,
  averageWaitTime: 22,
  averageServiceTime: 16,
  peakHours: ["10:00-11:00", "14:00-15:00", "16:00-17:00"],
  serviceStats: {
    [ServiceType.Consultation]: {
      totalProcessed: 25,
      averageWaitTime: 25,
      averageServiceTime: 18,
      currentInQueue: 4
    },
    [ServiceType.Emergency]: {
      totalProcessed: 8,
      averageWaitTime: 5,
      averageServiceTime: 30,
      currentInQueue: 1
    },
    [ServiceType.Laboratory]: {
      totalProcessed: 12,
      averageWaitTime: 12,
      averageServiceTime: 8,
      currentInQueue: 2
    },
    [ServiceType.Diagnostic]: {
      totalProcessed: 6,
      averageWaitTime: 35,
      averageServiceTime: 25,
      currentInQueue: 3
    },
    [ServiceType.Pharmacy]: {
      totalProcessed: 15,
      averageWaitTime: 8,
      averageServiceTime: 5,
      currentInQueue: 2
    }
  },
  hourlyStats: [
    { hour: "09:00", patientsServed: 3, averageWaitTime: 15, queueLength: 5 },
    { hour: "10:00", patientsServed: 8, averageWaitTime: 25, queueLength: 12 },
    { hour: "11:00", patientsServed: 6, averageWaitTime: 20, queueLength: 8 },
    { hour: "12:00", patientsServed: 4, averageWaitTime: 18, queueLength: 6 },
    { hour: "13:00", patientsServed: 2, averageWaitTime: 10, queueLength: 3 },
    { hour: "14:00", patientsServed: 7, averageWaitTime: 28, queueLength: 10 },
    { hour: "15:00", patientsServed: 5, averageWaitTime: 22, queueLength: 7 },
    { hour: "16:00", patientsServed: 6, averageWaitTime: 30, queueLength: 9 },
    { hour: "17:00", patientsServed: 4, averageWaitTime: 15, queueLength: 4 }
  ],
  patientSatisfactionScore: 4.2,
  noShowRate: 8.5
};

// Enhanced patient names for more realistic testing
const enhancedPatientNames = [
  { firstName: "Rajesh", lastName: "Kumar", gender: "Male", age: 45 },
  { firstName: "Priya", lastName: "Sharma", gender: "Female", age: 32 },
  { firstName: "Amit", lastName: "Singh", gender: "Male", age: 28 },
  { firstName: "Sunita", lastName: "Patel", gender: "Female", age: 55 },
  { firstName: "Vikram", lastName: "Gupta", gender: "Male", age: 38 },
  { firstName: "Meera", lastName: "Joshi", gender: "Female", age: 42 },
  { firstName: "Arjun", lastName: "Reddy", gender: "Male", age: 35 },
  { firstName: "Kavya", lastName: "Nair", gender: "Female", age: 29 },
  { firstName: "Rohit", lastName: "Agarwal", gender: "Male", age: 51 },
  { firstName: "Deepika", lastName: "Verma", gender: "Female", age: 26 },
  { firstName: "Suresh", lastName: "Yadav", gender: "Male", age: 48 },
  { firstName: "Anita", lastName: "Mishra", gender: "Female", age: 39 },
  { firstName: "Kiran", lastName: "Thakur", gender: "Male", age: 33 },
  { firstName: "Pooja", lastName: "Bansal", gender: "Female", age: 31 },
  { firstName: "Manoj", lastName: "Chopra", gender: "Male", age: 44 },
  { firstName: "Ritu", lastName: "Saxena", gender: "Female", age: 37 },
  { firstName: "Sanjay", lastName: "Malhotra", gender: "Male", age: 52 },
  { firstName: "Neha", lastName: "Kapoor", gender: "Female", age: 24 },
  { firstName: "Ashok", lastName: "Pandey", gender: "Male", age: 58 },
  { firstName: "Shweta", lastName: "Sinha", gender: "Female", age: 34 }
];

// Mobile number generator
const generateMobileNumber = () => {
  const prefixes = ['98', '97', '96', '95', '94', '93', '92', '91', '90', '89'];
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
  const remaining = Math.floor(Math.random() * 100000000).toString().padStart(8, '0');
  return `+91-${prefix}${remaining}`;
};

// Generate comprehensive mock queue entries for all departments
export const generateMockQueueEntries = (count: number = 50): QueueEntry[] => {
  const services = Object.values(ServiceType);
  const priorities = Object.values(AppointmentPriority);
  const entries: QueueEntry[] = [];

  // Create realistic distribution of patients across departments
  services.forEach((serviceType, serviceIndex) => {
    const patientsForService = Math.floor(count / services.length) + (serviceIndex < count % services.length ? 1 : 0);

    for (let i = 0; i < patientsForService; i++) {
      const patientData = enhancedPatientNames[Math.floor(Math.random() * enhancedPatientNames.length)];
      const priority = priorities[Math.floor(Math.random() * priorities.length)];

      // Realistic status distribution
      let status: QueueStatus;
      const rand = Math.random();
      if (rand < 0.1 && i === 0) status = QueueStatus.InService; // Only one patient in service per department
      else if (rand < 0.15) status = QueueStatus.Called;
      else if (rand < 0.85) status = QueueStatus.Waiting;
      else status = QueueStatus.Completed;

      const baseWaitTime = serviceType === ServiceType.Emergency ? 5 :
                          serviceType === ServiceType.Laboratory ? 12 :
                          serviceType === ServiceType.Pharmacy ? 8 :
                          serviceType === ServiceType.Radiology ? 25 :
                          serviceType === ServiceType.Cardiology ? 30 :
                          serviceType === ServiceType.Orthopedics ? 20 :
                          serviceType === ServiceType.Pediatrics ? 15 :
                          serviceType === ServiceType.Gynecology ? 18 :
                          serviceType === ServiceType.Dermatology ? 22 : 20;

      const priorityMultiplier = priority === AppointmentPriority.Emergency ? 0.1 :
                                priority === AppointmentPriority.Urgent ? 0.3 :
                                priority === AppointmentPriority.High ? 0.6 :
                                priority === AppointmentPriority.Normal ? 1.0 : 1.5;

      const queueNumber = i + 1;
      const entryIndex = entries.length;

      entries.push({
        queueId: `q-${String(entryIndex + 100).padStart(3, '0')}`,
        appointmentId: Math.random() > 0.3 ? `apt-${String(entryIndex + 100).padStart(3, '0')}` : undefined,
        patientId: `pat-${String(entryIndex + 100).padStart(3, '0')}`,
        patientName: `${patientData.firstName} ${patientData.lastName}`,
        queueNumber,
        serviceType,
        priority,
        status,
        estimatedWaitTime: Math.round(baseWaitTime * priorityMultiplier * queueNumber * 0.8),
        estimatedServiceTime: `${String(Math.floor(Math.random() * 8) + 9).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`,
        actualWaitTime: status === QueueStatus.InService ? Math.round(baseWaitTime * priorityMultiplier) : undefined,
        joinedAt: new Date(Date.now() - Math.random() * 3600000).toISOString(),
        calledAt: status === QueueStatus.Called || status === QueueStatus.InService ?
          new Date(Date.now() - Math.random() * 600000).toISOString() : undefined,
        serviceStartedAt: status === QueueStatus.InService ?
          new Date(Date.now() - Math.random() * 300000).toISOString() : undefined,
        facilityId: "1",
        providerId: Math.random() > 0.5 ? `prov-${String(Math.floor(Math.random() * 5) + 1).padStart(3, '0')}` : undefined,
        patient: {
          firstName: patientData.firstName,
          lastName: patientData.lastName,
          mobileNumber: generateMobileNumber(),
          age: patientData.age,
          gender: patientData.gender
        },
        provider: Math.random() > 0.5 ? {
          firstName: "Dr. " + (Math.random() > 0.5 ? "Rajesh" : "Priya"),
          lastName: Math.random() > 0.5 ? "Kumar" : "Sharma",
          title: "Dr.",
          specialization: serviceType.charAt(0).toUpperCase() + serviceType.slice(1)
        } : undefined
      });
    }
  });

  return entries;
};

// Generate comprehensive mock data for all departments
export const generateComprehensiveMockData = (facilityId: string = "1") => {
  const allEntries = generateMockQueueEntries(60);
  const queueSummaries: { [key in ServiceType]?: QueueSummary } = {};

  Object.values(ServiceType).forEach(serviceType => {
    const serviceEntries = allEntries.filter(entry => entry.serviceType === serviceType);
    const activeEntries = serviceEntries.filter(entry => entry.status !== QueueStatus.Completed);
    const currentlyServing = serviceEntries.find(entry => entry.status === QueueStatus.InService);

    queueSummaries[serviceType] = {
      serviceType,
      facilityId,
      date: new Date().toISOString().split('T')[0],
      totalInQueue: activeEntries.length,
      currentlyServing: currentlyServing ? {
        queueEntry: currentlyServing,
        serviceStartTime: currentlyServing.serviceStartedAt || new Date().toISOString()
      } : undefined,
      queue: activeEntries,
      averageServiceTime: serviceType === ServiceType.Emergency ? 30 :
                         serviceType === ServiceType.Laboratory ? 10 :
                         serviceType === ServiceType.Pharmacy ? 5 :
                         serviceType === ServiceType.Radiology ? 25 :
                         serviceType === ServiceType.Cardiology ? 35 :
                         serviceType === ServiceType.Orthopedics ? 20 :
                         serviceType === ServiceType.Pediatrics ? 15 :
                         serviceType === ServiceType.Gynecology ? 18 :
                         serviceType === ServiceType.Dermatology ? 22 : 18,
      estimatedWaitTime: activeEntries.length > 0 ? activeEntries[0].estimatedWaitTime : 0,
      peakHours: ["10:00-11:00", "14:00-15:00", "16:00-17:00"],
      lastUpdated: new Date().toISOString()
    };
  });

  return queueSummaries;
};

// Mock data for doctor interface testing
export const mockDoctorQueueData: QueueSummary = {
  serviceType: ServiceType.Consultation,
  facilityId: "1",
  date: new Date().toISOString().split('T')[0],
  totalInQueue: 8,
  currentlyServing: {
    queueEntry: {
      queueId: "q-current-001",
      appointmentId: "apt-current-001",
      patientId: "pat-current-001",
      patientName: "Rajesh Kumar",
      queueNumber: 5,
      serviceType: ServiceType.Consultation,
      priority: AppointmentPriority.Normal,
      status: QueueStatus.InService,
      estimatedWaitTime: 0,
      estimatedServiceTime: "10:30",
      actualWaitTime: 15,
      joinedAt: new Date(Date.now() - 3600000).toISOString(),
      calledAt: new Date(Date.now() - 900000).toISOString(),
      serviceStartedAt: new Date(Date.now() - 300000).toISOString(),
      facilityId: "1",
      providerId: "prov-001",
      patient: {
        firstName: "Rajesh",
        lastName: "Kumar",
        mobileNumber: "+91-**********",
        age: 45,
        gender: "Male"
      },
      provider: {
        firstName: "Dr. Priya",
        lastName: "Sharma",
        title: "Dr.",
        specialization: "General Medicine"
      }
    },
    serviceStartTime: new Date(Date.now() - 300000).toISOString()
  },
  queue: [
    {
      queueId: "q-waiting-001",
      appointmentId: "apt-waiting-001",
      patientId: "pat-waiting-001",
      patientName: "Priya Sharma",
      queueNumber: 6,
      serviceType: ServiceType.Consultation,
      priority: AppointmentPriority.High,
      status: QueueStatus.Waiting,
      estimatedWaitTime: 5,
      estimatedServiceTime: "10:45",
      joinedAt: new Date(Date.now() - 2700000).toISOString(),
      facilityId: "1",
      providerId: "prov-001",
      patient: {
        firstName: "Priya",
        lastName: "Sharma",
        mobileNumber: "+91-**********",
        age: 32,
        gender: "Female"
      }
    },
    {
      queueId: "q-waiting-002",
      appointmentId: "apt-waiting-002",
      patientId: "pat-waiting-002",
      patientName: "Amit Singh",
      queueNumber: 7,
      serviceType: ServiceType.Consultation,
      priority: AppointmentPriority.Normal,
      status: QueueStatus.Waiting,
      estimatedWaitTime: 25,
      estimatedServiceTime: "11:00",
      joinedAt: new Date(Date.now() - 2400000).toISOString(),
      facilityId: "1",
      providerId: "prov-001",
      patient: {
        firstName: "Amit",
        lastName: "Singh",
        mobileNumber: "+91-**********",
        age: 28,
        gender: "Male"
      }
    },
    {
      queueId: "q-waiting-003",
      patientId: "pat-waiting-003",
      patientName: "Sunita Patel",
      queueNumber: 8,
      serviceType: ServiceType.Consultation,
      priority: AppointmentPriority.Normal,
      status: QueueStatus.Waiting,
      estimatedWaitTime: 40,
      estimatedServiceTime: "11:15",
      joinedAt: new Date(Date.now() - 2100000).toISOString(),
      facilityId: "1",
      patient: {
        firstName: "Sunita",
        lastName: "Patel",
        mobileNumber: "+91-**********",
        age: 55,
        gender: "Female"
      }
    },
    {
      queueId: "q-waiting-004",
      patientId: "pat-waiting-004",
      patientName: "Vikram Gupta",
      queueNumber: 9,
      serviceType: ServiceType.Consultation,
      priority: AppointmentPriority.Normal,
      status: QueueStatus.Waiting,
      estimatedWaitTime: 55,
      estimatedServiceTime: "11:30",
      joinedAt: new Date(Date.now() - 1800000).toISOString(),
      facilityId: "1",
      patient: {
        firstName: "Vikram",
        lastName: "Gupta",
        mobileNumber: "+91-**********",
        age: 38,
        gender: "Male"
      }
    },
    {
      queueId: "q-waiting-005",
      patientId: "pat-waiting-005",
      patientName: "Meera Joshi",
      queueNumber: 10,
      serviceType: ServiceType.Consultation,
      priority: AppointmentPriority.Low,
      status: QueueStatus.Waiting,
      estimatedWaitTime: 70,
      estimatedServiceTime: "11:45",
      joinedAt: new Date(Date.now() - 1500000).toISOString(),
      facilityId: "1",
      patient: {
        firstName: "Meera",
        lastName: "Joshi",
        mobileNumber: "+91-**********",
        age: 42,
        gender: "Female"
      }
    }
  ],
  averageServiceTime: 18,
  estimatedWaitTime: 5,
  peakHours: ["10:00-11:00", "14:00-15:00", "16:00-17:00"],
  lastUpdated: new Date().toISOString()
};
