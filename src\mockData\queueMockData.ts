import { 
  type QueueEntry, 
  type QueueSummary, 
  type QueueStatistics, 
  type WaitTimeEstimation,
  QueueStatus,
  ServiceType 
} from '../types/queue';
import { AppointmentPriority } from '../types/appointmentenums';

// Mock Queue Entries
export const mockQueueEntries: QueueEntry[] = [
  {
    queueId: "q-001",
    appointmentId: "apt-001",
    patientId: "pat-001",
    patientName: "John Doe",
    queueNumber: 1,
    serviceType: ServiceType.Consultation,
    priority: AppointmentPriority.Normal,
    status: QueueStatus.InService,
    estimatedWaitTime: 0,
    estimatedServiceTime: "10:30",
    actualWaitTime: 15,
    joinedAt: "2024-01-15T09:45:00Z",
    calledAt: "2024-01-15T10:00:00Z",
    serviceStartedAt: "2024-01-15T10:15:00Z",
    facilityId: "fac-001",
    providerId: "prov-001",
    patient: {
      firstName: "<PERSON>",
      lastName: "<PERSON><PERSON>",
      mobileNumber: "+91-**********",
      age: 35,
      gender: "Male"
    },
    provider: {
      firstName: "Dr. <PERSON>",
      lastName: "<PERSON>",
      title: "Dr.",
      specialization: "Cardiology"
    }
  },
  {
    queueId: "q-002",
    appointmentId: "apt-002",
    patientId: "pat-002",
    patientName: "Jane Smith",
    queueNumber: 2,
    serviceType: ServiceType.Consultation,
    priority: AppointmentPriority.High,
    status: QueueStatus.Called,
    estimatedWaitTime: 5,
    estimatedServiceTime: "10:45",
    joinedAt: "2024-01-15T09:50:00Z",
    calledAt: "2024-01-15T10:25:00Z",
    facilityId: "fac-001",
    providerId: "prov-001",
    patient: {
      firstName: "Jane",
      lastName: "Smith",
      mobileNumber: "+91-**********",
      age: 28,
      gender: "Female"
    },
    provider: {
      firstName: "Dr. Sarah",
      lastName: "Smith",
      title: "Dr.",
      specialization: "Cardiology"
    }
  },
  {
    queueId: "q-003",
    appointmentId: "apt-003",
    patientId: "pat-003",
    patientName: "Robert Johnson",
    queueNumber: 3,
    serviceType: ServiceType.Consultation,
    priority: AppointmentPriority.Normal,
    status: QueueStatus.Waiting,
    estimatedWaitTime: 25,
    estimatedServiceTime: "11:00",
    joinedAt: "2024-01-15T09:55:00Z",
    facilityId: "fac-001",
    providerId: "prov-001",
    patient: {
      firstName: "Robert",
      lastName: "Johnson",
      mobileNumber: "+91-**********",
      age: 42,
      gender: "Male"
    },
    provider: {
      firstName: "Dr. Sarah",
      lastName: "Smith",
      title: "Dr.",
      specialization: "Cardiology"
    }
  },
  {
    queueId: "q-004",
    patientId: "pat-004",
    patientName: "Emergency Patient",
    queueNumber: 1,
    serviceType: ServiceType.Emergency,
    priority: AppointmentPriority.Emergency,
    status: QueueStatus.Waiting,
    estimatedWaitTime: 2,
    estimatedServiceTime: "10:32",
    joinedAt: "2024-01-15T10:30:00Z",
    facilityId: "fac-001",
    patient: {
      firstName: "Emergency",
      lastName: "Patient",
      mobileNumber: "+91-**********",
      age: 55,
      gender: "Male"
    }
  },
  {
    queueId: "q-005",
    appointmentId: "apt-005",
    patientId: "pat-005",
    patientName: "Maria Garcia",
    queueNumber: 4,
    serviceType: ServiceType.Consultation,
    priority: AppointmentPriority.Normal,
    status: QueueStatus.Waiting,
    estimatedWaitTime: 40,
    estimatedServiceTime: "11:15",
    joinedAt: "2024-01-15T10:00:00Z",
    facilityId: "fac-001",
    providerId: "prov-001",
    patient: {
      firstName: "Maria",
      lastName: "Garcia",
      mobileNumber: "+91-**********",
      age: 31,
      gender: "Female"
    },
    provider: {
      firstName: "Dr. Sarah",
      lastName: "Smith",
      title: "Dr.",
      specialization: "Cardiology"
    }
  },
  {
    queueId: "q-006",
    patientId: "pat-006",
    patientName: "David Wilson",
    queueNumber: 1,
    serviceType: ServiceType.Laboratory,
    priority: AppointmentPriority.Normal,
    status: QueueStatus.Waiting,
    estimatedWaitTime: 10,
    estimatedServiceTime: "10:40",
    joinedAt: "2024-01-15T10:20:00Z",
    facilityId: "fac-001",
    patient: {
      firstName: "David",
      lastName: "Wilson",
      mobileNumber: "+91-**********",
      age: 38,
      gender: "Male"
    }
  },
  {
    queueId: "q-007",
    patientId: "pat-007",
    patientName: "Lisa Brown",
    queueNumber: 2,
    serviceType: ServiceType.Laboratory,
    priority: AppointmentPriority.Normal,
    status: QueueStatus.Waiting,
    estimatedWaitTime: 20,
    estimatedServiceTime: "10:50",
    joinedAt: "2024-01-15T10:25:00Z",
    facilityId: "fac-001",
    patient: {
      firstName: "Lisa",
      lastName: "Brown",
      mobileNumber: "+91-**********",
      age: 29,
      gender: "Female"
    }
  }
];

// Mock Queue Summary
export const mockQueueSummary: QueueSummary = {
  serviceType: ServiceType.Consultation,
  facilityId: "fac-001",
  date: "2024-01-15",
  totalInQueue: 4,
  currentlyServing: {
    queueEntry: mockQueueEntries[0],
    serviceStartTime: "10:15"
  },
  queue: mockQueueEntries.filter(entry => 
    entry.serviceType === ServiceType.Consultation && 
    entry.status !== QueueStatus.Completed
  ),
  averageServiceTime: 18,
  estimatedWaitTime: 25,
  peakHours: ["10:00-11:00", "14:00-15:00", "16:00-17:00"],
  lastUpdated: "2024-01-15T10:30:00Z"
};

// Mock Wait Time Estimations
export const mockWaitTimeEstimations: { [key in ServiceType]?: WaitTimeEstimation } = {
  [ServiceType.Consultation]: {
    serviceType: ServiceType.Consultation,
    facilityId: "fac-001",
    estimatedWaitTime: 25,
    queuePosition: 3,
    patientsAhead: 2,
    averageServiceTime: 18,
    currentWaitTime: {
      [AppointmentPriority.Low]: 45,
      [AppointmentPriority.Normal]: 25,
      [AppointmentPriority.High]: 15,
      [AppointmentPriority.Urgent]: 8,
      [AppointmentPriority.Emergency]: 2
    },
    lastUpdated: "2024-01-15T10:30:00Z",
    confidence: 85
  },
  [ServiceType.Emergency]: {
    serviceType: ServiceType.Emergency,
    facilityId: "fac-001",
    estimatedWaitTime: 2,
    queuePosition: 1,
    patientsAhead: 0,
    averageServiceTime: 30,
    currentWaitTime: {
      [AppointmentPriority.Low]: 60,
      [AppointmentPriority.Normal]: 45,
      [AppointmentPriority.High]: 30,
      [AppointmentPriority.Urgent]: 15,
      [AppointmentPriority.Emergency]: 2
    },
    lastUpdated: "2024-01-15T10:30:00Z",
    confidence: 95
  },
  [ServiceType.Laboratory]: {
    serviceType: ServiceType.Laboratory,
    facilityId: "fac-001",
    estimatedWaitTime: 15,
    queuePosition: 2,
    patientsAhead: 1,
    averageServiceTime: 10,
    currentWaitTime: {
      [AppointmentPriority.Low]: 25,
      [AppointmentPriority.Normal]: 15,
      [AppointmentPriority.High]: 10,
      [AppointmentPriority.Urgent]: 5,
      [AppointmentPriority.Emergency]: 1
    },
    lastUpdated: "2024-01-15T10:30:00Z",
    confidence: 90
  }
};

// Mock Queue Statistics
export const mockQueueStatistics: QueueStatistics = {
  facilityId: "fac-001",
  date: "2024-01-15",
  totalProcessed: 45,
  currentInQueue: 12,
  averageWaitTime: 22,
  averageServiceTime: 16,
  peakHours: ["10:00-11:00", "14:00-15:00", "16:00-17:00"],
  serviceStats: {
    [ServiceType.Consultation]: {
      totalProcessed: 25,
      averageWaitTime: 25,
      averageServiceTime: 18,
      currentInQueue: 4
    },
    [ServiceType.Emergency]: {
      totalProcessed: 8,
      averageWaitTime: 5,
      averageServiceTime: 30,
      currentInQueue: 1
    },
    [ServiceType.Laboratory]: {
      totalProcessed: 12,
      averageWaitTime: 12,
      averageServiceTime: 8,
      currentInQueue: 2
    },
    [ServiceType.Diagnostic]: {
      totalProcessed: 6,
      averageWaitTime: 35,
      averageServiceTime: 25,
      currentInQueue: 3
    },
    [ServiceType.Pharmacy]: {
      totalProcessed: 15,
      averageWaitTime: 8,
      averageServiceTime: 5,
      currentInQueue: 2
    }
  },
  hourlyStats: [
    { hour: "09:00", patientsServed: 3, averageWaitTime: 15, queueLength: 5 },
    { hour: "10:00", patientsServed: 8, averageWaitTime: 25, queueLength: 12 },
    { hour: "11:00", patientsServed: 6, averageWaitTime: 20, queueLength: 8 },
    { hour: "12:00", patientsServed: 4, averageWaitTime: 18, queueLength: 6 },
    { hour: "13:00", patientsServed: 2, averageWaitTime: 10, queueLength: 3 },
    { hour: "14:00", patientsServed: 7, averageWaitTime: 28, queueLength: 10 },
    { hour: "15:00", patientsServed: 5, averageWaitTime: 22, queueLength: 7 },
    { hour: "16:00", patientsServed: 6, averageWaitTime: 30, queueLength: 9 },
    { hour: "17:00", patientsServed: 4, averageWaitTime: 15, queueLength: 4 }
  ],
  patientSatisfactionScore: 4.2,
  noShowRate: 8.5
};

// Generate additional queue entries for different services
export const generateMockQueueEntries = (count: number = 20): QueueEntry[] => {
  const services = Object.values(ServiceType);
  const priorities = Object.values(AppointmentPriority);
  const statuses = Object.values(QueueStatus);
  const names = [
    "Alice Johnson", "Bob Smith", "Carol Davis", "David Brown", "Eva Wilson",
    "Frank Miller", "Grace Lee", "Henry Taylor", "Ivy Chen", "Jack Anderson",
    "Kate Thompson", "Liam Garcia", "Mia Rodriguez", "Noah Martinez", "Olivia Lopez"
  ];

  return Array.from({ length: count }, (_, index) => {
    const serviceType = services[Math.floor(Math.random() * services.length)];
    const priority = priorities[Math.floor(Math.random() * priorities.length)];
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const name = names[Math.floor(Math.random() * names.length)];
    const [firstName, lastName] = name.split(' ');

    const baseWaitTime = serviceType === ServiceType.Emergency ? 5 :
                        serviceType === ServiceType.Laboratory ? 12 :
                        serviceType === ServiceType.Pharmacy ? 8 : 20;

    const priorityMultiplier = priority === AppointmentPriority.Emergency ? 0.1 :
                              priority === AppointmentPriority.Urgent ? 0.3 :
                              priority === AppointmentPriority.High ? 0.6 :
                              priority === AppointmentPriority.Normal ? 1.0 : 1.5;

    return {
      queueId: `q-${String(index + 100).padStart(3, '0')}`,
      appointmentId: Math.random() > 0.3 ? `apt-${String(index + 100).padStart(3, '0')}` : undefined,
      patientId: `pat-${String(index + 100).padStart(3, '0')}`,
      patientName: name,
      queueNumber: Math.floor(Math.random() * 20) + 1,
      serviceType,
      priority,
      status,
      estimatedWaitTime: Math.round(baseWaitTime * priorityMultiplier),
      estimatedServiceTime: `${String(Math.floor(Math.random() * 8) + 9).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`,
      joinedAt: new Date(Date.now() - Math.random() * 3600000).toISOString(),
      facilityId: "fac-001",
      providerId: Math.random() > 0.5 ? `prov-${String(Math.floor(Math.random() * 5) + 1).padStart(3, '0')}` : undefined,
      patient: {
        firstName,
        lastName,
        mobileNumber: `+91-98765432${String(Math.floor(Math.random() * 100)).padStart(2, '0')}`,
        age: Math.floor(Math.random() * 60) + 18,
        gender: Math.random() > 0.5 ? "Male" : "Female"
      }
    };
  });
};
