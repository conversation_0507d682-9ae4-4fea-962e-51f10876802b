import React, { useState, useEffect } from "react";
import { Calendar, Clock, Users, Plus, Filter, Search, Edit, Trash2, X, Check, XCircle } from "lucide-react";
import { AppointmentCalendar } from "../components/appointments/AppointmentCalendar";

import AppointmentSidePanel from "../components/appointments/AppointmentSidePanel";
import { useAppointmentStore } from "../store/appointmentStore";
import { useProviderStore } from "../store/providerStore";
import { getAppointmentStats, deleteAppointment, cancelAppointment, confirmAppointment } from "../services/appointmentApis";
import { getProviders } from "../services/providerApis";
import { Button } from "../commonfields/Button";
import { Select } from "../commonfields/Select";
import { Input } from "../commonfields/Input";
import { showError, showSuccess } from "../utils/toastUtils";
import type { Appointment, AppointmentStats } from "../types/appointment";
import type { Provider } from "../types/provider";

const AppointmentsPage: React.FC = () => {
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [showSidePanel, setShowSidePanel] = useState(false);
  const [selectedSlotDate, setSelectedSlotDate] = useState<string>("");
  const [selectedSlotTime, setSelectedSlotTime] = useState<string>("");
  const [editingAppointment, setEditingAppointment] = useState<Appointment | null>(null);
  const [selectedProviderId, setSelectedProviderId] = useState<string>("");
  const [searchTerm, setSearchTerm] = useState("");
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deletingAppointment, setDeletingAppointment] = useState<Appointment | null>(null);
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);
  const [cancellingAppointment, setCancellingAppointment] = useState<Appointment | null>(null);
  const [cancelReason, setCancelReason] = useState("");
  const [cancelNotes, setCancelNotes] = useState("");
  const [notifyPatient, setNotifyPatient] = useState(true);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [confirmingAppointment, setConfirmingAppointment] = useState<Appointment | null>(null);
  const [confirmNotes, setConfirmNotes] = useState("");
  const [notifyPatientConfirm, setNotifyPatientConfirm] = useState(true);

  const {
    appointmentStats,
    setAppointmentStats,
    selectedDate,
    setSelectedDate,
    removeAppointment,
    updateAppointment: updateAppointmentInStore
  } = useAppointmentStore();

  const { 
    providers, 
    setProviders,
    loading: providersLoading 
  } = useProviderStore();

  useEffect(() => {
    loadInitialData();
  }, []);



  const loadInitialData = async () => {
    try {
      // Load providers
      const providersResponse = await getProviders({ isActive: true, size: 100 });
      setProviders(providersResponse.results || []);

      // Load appointment statistics
      const stats = await getAppointmentStats();
      setAppointmentStats(stats);
    } catch (error) {
      console.error("Failed to load initial data:", error);
      showError("Failed to load appointment data");
    }
  };

  const handleSelectAppointment = (appointment: Appointment) => {
    setSelectedAppointment(appointment);
    // You can open a modal or navigate to appointment details here
    console.log("Selected appointment:", appointment);
  };

  const handleSelectSlot = (slotInfo: { start: Date; end: Date; slots: Date[] }) => {
    // Pre-fill form with selected time slot
    const appointmentDate = slotInfo.start.toISOString().split('T')[0];
    const startTime = slotInfo.start.toTimeString().slice(0, 5);

    // Set the selected date and time for the side panel
    setSelectedSlotDate(appointmentDate);
    setSelectedSlotTime(startTime);
    setEditingAppointment(null); // Clear any editing appointment
    setShowSidePanel(true);
    console.log("Selected slot:", { appointmentDate, startTime });
  };

  const handleSidePanelClose = () => {
    setShowSidePanel(false);
    setSelectedSlotDate("");
    setSelectedSlotTime("");
    setEditingAppointment(null);
  };

  const handleAppointmentCreated = () => {
    // Refresh data
    loadInitialData();
  };

  const handleEditAppointment = () => {
    if (selectedAppointment) {
      setEditingAppointment(selectedAppointment);
      setSelectedSlotDate(selectedAppointment.appointmentDate.split('T')[0]);
      setSelectedSlotTime(selectedAppointment.startTime);
      setShowSidePanel(true);
      setSelectedAppointment(null); // Close details modal
    }
  };

  const handleDeleteClick = (appointment: Appointment) => {
    setDeletingAppointment(appointment);
    setShowDeleteConfirm(true);
    setSelectedAppointment(null); // Close details modal
  };

  const handleDeleteConfirm = async () => {
    if (!deletingAppointment?.appointmentId) return;

    try {
      const result = await deleteAppointment(deletingAppointment.appointmentId);
      if (result.success) {
        removeAppointment(deletingAppointment.appointmentId);
        showSuccess("Appointment deleted successfully");
        setShowDeleteConfirm(false);
        setDeletingAppointment(null);
        // Refresh data
        loadInitialData();
      } else {
        showError(result.error || "Failed to delete appointment");
      }
    } catch (error) {
      console.error("Delete appointment error:", error);
      showError("An unexpected error occurred while deleting the appointment");
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteConfirm(false);
    setDeletingAppointment(null);
  };

  const handleCancelAppointment = (appointment: Appointment) => {
    setCancellingAppointment(appointment);
    setShowCancelConfirm(true);
    setSelectedAppointment(null); // Close details modal
    // Reset form
    setCancelReason("");
    setCancelNotes("");
    setNotifyPatient(true);
  };

  const handleCancelConfirm = async () => {
    if (!cancellingAppointment?.appointmentId || !cancelReason) return;

    try {
      const result = await cancelAppointment(
        cancellingAppointment.appointmentId,
        cancelReason,
        cancelNotes,
        notifyPatient
      );
      if (result.success) {
        updateAppointmentInStore(cancellingAppointment.appointmentId, { ...cancellingAppointment, status: 'Cancelled' as any });
        showSuccess("Appointment cancelled successfully");
        setShowCancelConfirm(false);
        setCancellingAppointment(null);
        // Reset form
        setCancelReason("");
        setCancelNotes("");
        setNotifyPatient(true);
        // Refresh data
        loadInitialData();
      } else {
        showError(result.error || "Failed to cancel appointment");
      }
    } catch (error) {
      console.error("Cancel appointment error:", error);
      showError("An unexpected error occurred while cancelling the appointment");
    }
  };

  const handleCancelCancel = () => {
    setShowCancelConfirm(false);
    setCancellingAppointment(null);
    // Reset form
    setCancelReason("");
    setCancelNotes("");
    setNotifyPatient(true);
  };

  const handleConfirmAppointment = (appointment: Appointment) => {
    setConfirmingAppointment(appointment);
    setShowConfirmModal(true);
    setSelectedAppointment(null); // Close details modal
    // Reset form
    setConfirmNotes("");
    setNotifyPatientConfirm(true);
  };

  const handleConfirmConfirm = async () => {
    if (!confirmingAppointment?.appointmentId) return;

    try {
      const result = await confirmAppointment(
        confirmingAppointment.appointmentId,
        confirmNotes,
        notifyPatientConfirm
      );
      if (result.success) {
        updateAppointmentInStore(confirmingAppointment.appointmentId, { ...confirmingAppointment, status: 'Confirmed' as any });
        showSuccess("Appointment confirmed successfully");
        setShowConfirmModal(false);
        setConfirmingAppointment(null);
        // Reset form
        setConfirmNotes("");
        setNotifyPatientConfirm(true);
        // Refresh data
        loadInitialData();
      } else {
        showError(result.error || "Failed to confirm appointment");
      }
    } catch (error) {
      console.error("Confirm appointment error:", error);
      showError("An unexpected error occurred while confirming the appointment");
    }
  };

  const handleConfirmCancel = () => {
    setShowConfirmModal(false);
    setConfirmingAppointment(null);
    // Reset form
    setConfirmNotes("");
    setNotifyPatientConfirm(true);
  };



  const StatCard = ({ 
    title, 
    value, 
    icon, 
    color = "blue" 
  }: { 
    title: string; 
    value: string | number; 
    icon: React.ReactNode; 
    color?: string;
  }) => (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
        </div>
        <div className={`p-3 rounded-full bg-${color}-100 text-${color}-600`}>
          {icon}
        </div>
      </div>
    </div>
  );

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Appointments</h1>
          <p className="text-gray-600">Manage and schedule appointments</p>
        </div>
        <div className="flex space-x-3">
          <Button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
          >
            <Filter size={16} />
            <span>Filters</span>
          </Button>
          <Button
            onClick={() => {
              setSelectedSlotDate("");
              setSelectedSlotTime("");
              setEditingAppointment(null);
              setShowSidePanel(true);
            }}
            className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
          >
            <Plus size={16} />
            <span>New Appointment</span>
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      {appointmentStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <StatCard
            title="Today's Appointments"
            value={appointmentStats.todayAppointments}
            icon={<Calendar size={20} />}
            color="blue"
          />
          <StatCard
            title="Upcoming"
            value={appointmentStats.upcomingAppointments}
            icon={<Clock size={20} />}
            color="green"
          />
          <StatCard
            title="Completed"
            value={appointmentStats.completedAppointments}
            icon={<Users size={20} />}
            color="gray"
          />
          <StatCard
            title="Total"
            value={appointmentStats.totalAppointments}
            icon={<Calendar size={20} />}
            color="purple"
          />
        </div>
      )}

      {/* Filters Panel */}
      {showFilters && (
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Provider
              </label>
              <Select
                value={selectedProviderId}
                onChange={(e) => setSelectedProviderId(e.target.value)}
              >
                <option value="">All Providers</option>
                {providers.map((provider) => (
                  <option key={provider.providerId} value={provider.providerId}>
                    {provider.title} {provider.firstName} {provider.lastName}
                  </option>
                ))}
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date
              </label>
              <Input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Search
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <Input
                  placeholder="Search appointments..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Calendar View */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <AppointmentCalendar
          onSelectAppointment={handleSelectAppointment}
          onSelectSlot={handleSelectSlot}
          selectedProviderId={selectedProviderId}
          facilityId={undefined} // You can add facility filter here
        />
      </div>

      {/* Appointment Side Panel */}
      <AppointmentSidePanel
        isOpen={showSidePanel}
        onClose={handleSidePanelClose}
        selectedDate={selectedSlotDate}
        selectedTime={selectedSlotTime}
        onAppointmentCreated={handleAppointmentCreated}
        editingAppointment={editingAppointment}
      />

      {/* Appointment Details Modal */}
      {selectedAppointment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4">
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold text-gray-800">Appointment Details</h2>
                <div className="flex items-center space-x-2">
                  <Button
                    onClick={handleEditAppointment}
                    className="flex items-center space-x-1 px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                  >
                    <Edit size={14} />
                    <span>Edit</span>
                  </Button>
                  <Button
                    onClick={() => handleDeleteClick(selectedAppointment)}
                    className="flex items-center space-x-1 px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700"
                  >
                    <Trash2 size={14} />
                    <span>Delete</span>
                  </Button>
                  <button
                    onClick={() => setSelectedAppointment(null)}
                    className="text-gray-400 hover:text-gray-600 p-1"
                  >
                    <X size={20} />
                  </button>
                </div>
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Patient</label>
                  <p className="text-gray-900">
                    {selectedAppointment.patient?.firstName} {selectedAppointment.patient?.lastName}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Provider</label>
                  <p className="text-gray-900">
                    {selectedAppointment.provider?.title} {selectedAppointment.provider?.firstName} {selectedAppointment.provider?.lastName}
                  </p>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Date</label>
                    <p className="text-gray-900">{selectedAppointment.appointmentDate}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Time</label>
                    <p className="text-gray-900">
                      {selectedAppointment.startTime} - {selectedAppointment.endTime}
                    </p>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Status</label>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    selectedAppointment.status === 'Confirmed' ? 'bg-green-100 text-green-800' :
                    selectedAppointment.status === 'Scheduled' ? 'bg-blue-100 text-blue-800' :
                    selectedAppointment.status === 'Cancelled' ? 'bg-red-100 text-red-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {selectedAppointment.status}
                  </span>
                </div>
                {selectedAppointment.notes && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Notes</label>
                    <p className="text-gray-900">{selectedAppointment.notes}</p>
                  </div>
                )}
              </div>

              {/* Quick Actions */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <h3 className="text-sm font-medium text-gray-700 mb-3">Quick Actions</h3>
                <div className="flex space-x-3">
                  {selectedAppointment.status !== 'Confirmed' && selectedAppointment.status !== 'Cancelled' && (
                    <Button
                      onClick={() => handleConfirmAppointment(selectedAppointment)}
                      className="flex items-center space-x-1 px-3 py-2 text-sm bg-green-600 text-white rounded hover:bg-green-700"
                    >
                      <Check size={14} />
                      <span>Confirm</span>
                    </Button>
                  )}
                  {selectedAppointment.status !== 'Cancelled' && selectedAppointment.status !== 'Completed' && (
                    <Button
                      onClick={() => handleCancelAppointment(selectedAppointment)}
                      className="flex items-center space-x-1 px-3 py-2 text-sm bg-orange-600 text-white rounded hover:bg-orange-700"
                    >
                      <XCircle size={14} />
                      <span>Cancel</span>
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && deletingAppointment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="flex-shrink-0">
                  <Trash2 className="h-6 w-6 text-red-600" />
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Delete Appointment</h3>
                  <p className="text-sm text-gray-500">
                    Are you sure you want to delete this appointment? This action cannot be undone.
                  </p>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg mb-4">
                <div className="text-sm">
                  <p><strong>Patient:</strong> {deletingAppointment.patient?.firstName} {deletingAppointment.patient?.lastName}</p>
                  <p><strong>Date:</strong> {deletingAppointment.appointmentDate}</p>
                  <p><strong>Time:</strong> {deletingAppointment.startTime} - {deletingAppointment.endTime}</p>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <Button
                  onClick={handleDeleteCancel}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleDeleteConfirm}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                >
                  Delete Appointment
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Cancel Appointment Modal */}
      {showCancelConfirm && cancellingAppointment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="flex-shrink-0">
                  <XCircle className="h-6 w-6 text-orange-600" />
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Cancel Appointment</h3>
                  <p className="text-sm text-gray-500">
                    Please provide a reason for cancelling this appointment.
                  </p>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg mb-4">
                <div className="text-sm">
                  <p><strong>Patient:</strong> {cancellingAppointment.patient?.firstName} {cancellingAppointment.patient?.lastName}</p>
                  <p><strong>Date:</strong> {cancellingAppointment.appointmentDate}</p>
                  <p><strong>Time:</strong> {cancellingAppointment.startTime} - {cancellingAppointment.endTime}</p>
                </div>
              </div>

              <div className="space-y-4 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Reason for Cancellation *
                  </label>
                  <Select
                    value={cancelReason}
                    onChange={(e) => setCancelReason(e.target.value)}
                    className="w-full"
                  >
                    <option value="">Select a reason</option>
                    <option value="Patient Request">Patient Request</option>
                    <option value="Provider Unavailable">Provider Unavailable</option>
                    <option value="Emergency">Emergency</option>
                    <option value="Facility Closure">Facility Closure</option>
                    <option value="Equipment Issue">Equipment Issue</option>
                    <option value="Weather Conditions">Weather Conditions</option>
                    <option value="Patient Illness">Patient Illness</option>
                    <option value="Scheduling Conflict">Scheduling Conflict</option>
                    <option value="Other">Other</option>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Additional Notes
                  </label>
                  <textarea
                    value={cancelNotes}
                    onChange={(e) => setCancelNotes(e.target.value)}
                    className="w-full px-3 py-2 text-sm rounded-md border border-gray-300 focus:border-black focus:ring-1 focus:ring-black focus:outline-none"
                    rows={3}
                    placeholder="Optional additional notes..."
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="notifyPatient"
                    checked={notifyPatient}
                    onChange={(e) => setNotifyPatient(e.target.checked)}
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  />
                  <label htmlFor="notifyPatient" className="ml-2 block text-sm text-gray-700">
                    Notify patient about cancellation
                  </label>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <Button
                  onClick={handleCancelCancel}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                  Keep Appointment
                </Button>
                <Button
                  onClick={handleCancelConfirm}
                  disabled={!cancelReason}
                  className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Cancel Appointment
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Confirm Appointment Modal */}
      {showConfirmModal && confirmingAppointment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="flex-shrink-0">
                  <Check className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Confirm Appointment</h3>
                  <p className="text-sm text-gray-500">
                    Confirm this appointment and optionally add notes.
                  </p>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg mb-4">
                <div className="text-sm">
                  <p><strong>Patient:</strong> {confirmingAppointment.patient?.firstName} {confirmingAppointment.patient?.lastName}</p>
                  <p><strong>Date:</strong> {confirmingAppointment.appointmentDate}</p>
                  <p><strong>Time:</strong> {confirmingAppointment.startTime} - {confirmingAppointment.endTime}</p>
                </div>
              </div>

              <div className="space-y-4 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Confirmation Notes
                  </label>
                  <textarea
                    value={confirmNotes}
                    onChange={(e) => setConfirmNotes(e.target.value)}
                    className="w-full px-3 py-2 text-sm rounded-md border border-gray-300 focus:border-black focus:ring-1 focus:ring-black focus:outline-none"
                    rows={3}
                    placeholder="Optional notes about the confirmation..."
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="notifyPatientConfirm"
                    checked={notifyPatientConfirm}
                    onChange={(e) => setNotifyPatientConfirm(e.target.checked)}
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  />
                  <label htmlFor="notifyPatientConfirm" className="ml-2 block text-sm text-gray-700">
                    Notify patient about confirmation
                  </label>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <Button
                  onClick={handleConfirmCancel}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleConfirmConfirm}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                >
                  Confirm Appointment
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AppointmentsPage;
