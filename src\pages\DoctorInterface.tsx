import React, { useState, useEffect } from 'react';
import { 
  User, 
  Phone, 
  Calendar, 
  Clock, 
  Users, 
  Play, 
  SkipForward, 
  Volume2,
  AlertCircle,
  CheckCircle,
  Timer
} from 'lucide-react';
import { Button } from '../commonfields/Button';
import { showSuccess, showError } from '../utils/toastUtils';
import { getServiceQueue, updateQueueStatus } from '../services/queueApis';
import { ServiceType, QueueStatus } from '../types/queue';
import type { QueueEntry, QueueSummary } from '../types/queue';

interface DoctorInterfaceProps {
  providerId: string;
  facilityId: string;
  serviceType?: ServiceType;
}

const DoctorInterface: React.FC<DoctorInterfaceProps> = ({
  providerId,
  facilityId,
  serviceType = ServiceType.Consultation
}) => {
  const [queueData, setQueueData] = useState<QueueSummary | null>(null);
  const [currentPatient, setCurrentPatient] = useState<QueueEntry | null>(null);
  const [loading, setLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    loadQueueData();
    // Refresh queue data every 30 seconds
    const interval = setInterval(loadQueueData, 30000);
    return () => clearInterval(interval);
  }, [serviceType, facilityId]);

  const loadQueueData = async () => {
    setLoading(true);
    try {
      const data = await getServiceQueue(serviceType, facilityId);
      setQueueData(data);
      
      // Set current patient if someone is being served
      if (data.currentlyServing) {
        setCurrentPatient(data.currentlyServing.queueEntry);
      } else {
        setCurrentPatient(null);
      }
    } catch (error) {
      console.error('Failed to load queue data:', error);
      showError('Failed to load queue data');
    } finally {
      setLoading(false);
    }
  };

  const callNextPatient = async () => {
    if (!queueData?.queue.length) {
      showError('No patients in queue');
      return;
    }

    setActionLoading(true);
    try {
      // Find the next waiting patient
      const nextPatient = queueData.queue.find(p => p.status === QueueStatus.Waiting);
      
      if (!nextPatient) {
        showError('No waiting patients in queue');
        return;
      }

      // Update status to Called
      const result = await updateQueueStatus(
        serviceType,
        nextPatient.queueId,
        QueueStatus.Called,
        'Called by doctor'
      );

      if (result.success) {
        showSuccess(`Called patient: ${nextPatient.patientName} (Token #${nextPatient.queueNumber})`);
        
        // Update to InService after a short delay
        setTimeout(async () => {
          await updateQueueStatus(
            serviceType,
            nextPatient.queueId,
            QueueStatus.InService,
            'Patient in consultation'
          );
          setCurrentPatient(nextPatient);
          await loadQueueData();
        }, 2000);
      } else {
        showError(result.error || 'Failed to call patient');
      }
    } catch (error) {
      console.error('Failed to call next patient:', error);
      showError('Failed to call next patient');
    } finally {
      setActionLoading(false);
    }
  };

  const skipPatient = async () => {
    if (!currentPatient) {
      showError('No current patient to skip');
      return;
    }

    setActionLoading(true);
    try {
      const result = await updateQueueStatus(
        serviceType,
        currentPatient.queueId,
        QueueStatus.Waiting,
        'Patient skipped - moved back to waiting'
      );

      if (result.success) {
        showSuccess(`Skipped patient: ${currentPatient.patientName}`);
        setCurrentPatient(null);
        await loadQueueData();
      } else {
        showError(result.error || 'Failed to skip patient');
      }
    } catch (error) {
      console.error('Failed to skip patient:', error);
      showError('Failed to skip patient');
    } finally {
      setActionLoading(false);
    }
  };

  const completeConsultation = async () => {
    if (!currentPatient) {
      showError('No current patient to complete');
      return;
    }

    setActionLoading(true);
    try {
      const result = await updateQueueStatus(
        serviceType,
        currentPatient.queueId,
        QueueStatus.Completed,
        'Consultation completed'
      );

      if (result.success) {
        showSuccess(`Completed consultation for: ${currentPatient.patientName}`);
        setCurrentPatient(null);
        await loadQueueData();
      } else {
        showError(result.error || 'Failed to complete consultation');
      }
    } catch (error) {
      console.error('Failed to complete consultation:', error);
      showError('Failed to complete consultation');
    } finally {
      setActionLoading(false);
    }
  };

  const makeAnnouncement = () => {
    if (!currentPatient) {
      showError('No current patient for announcement');
      return;
    }

    // In a real implementation, this would trigger an audio announcement
    showSuccess(`Announcement: ${currentPatient.patientName}, Token #${currentPatient.queueNumber}, please proceed to consultation room`);
  };

  const waitingPatients = queueData?.queue.filter(p => p.status === QueueStatus.Waiting) || [];
  const totalInQueue = queueData?.totalInQueue || 0;

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Doctor Interface</h1>
              <p className="text-gray-600 mt-1">
                {serviceType} - {totalInQueue} patients in queue
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-sm text-gray-500">Last Updated</p>
                <p className="text-sm font-medium">
                  {queueData?.lastUpdated ? new Date(queueData.lastUpdated).toLocaleTimeString() : 'Never'}
                </p>
              </div>
              <Button
                onClick={loadQueueData}
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                {loading ? 'Refreshing...' : 'Refresh'}
              </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Current Patient Section */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900">Current Patient</h2>
              {currentPatient && (
                <div className="flex items-center space-x-2 text-green-600">
                  <CheckCircle size={20} />
                  <span className="text-sm font-medium">In Consultation</span>
                </div>
              )}
            </div>

            {currentPatient ? (
              <div className="space-y-6">
                {/* Patient Info Card */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center space-x-4">
                    <div className="bg-blue-600 text-white rounded-full w-12 h-12 flex items-center justify-center text-xl font-bold">
                      {currentPatient.queueNumber}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {currentPatient.patientName}
                      </h3>
                      <div className="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600">
                        <div className="flex items-center space-x-2">
                          <User size={16} />
                          <span>Age: {currentPatient.patient?.age || 'N/A'}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Phone size={16} />
                          <span>{currentPatient.patient?.mobileNumber || 'N/A'}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Calendar size={16} />
                          <span>Gender: {currentPatient.patient?.gender || 'N/A'}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Timer size={16} />
                          <span>Wait: {currentPatient.actualWaitTime || currentPatient.estimatedWaitTime}min</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                  <Button
                    onClick={makeAnnouncement}
                    className="flex items-center justify-center space-x-2 px-4 py-3 bg-purple-600 text-white rounded-md hover:bg-purple-700"
                  >
                    <Volume2 size={18} />
                    <span>Announce</span>
                  </Button>
                  <Button
                    onClick={skipPatient}
                    disabled={actionLoading}
                    className="flex items-center justify-center space-x-2 px-4 py-3 bg-orange-600 text-white rounded-md hover:bg-orange-700"
                  >
                    <SkipForward size={18} />
                    <span>Skip</span>
                  </Button>
                  <Button
                    onClick={completeConsultation}
                    disabled={actionLoading}
                    className="flex items-center justify-center space-x-2 px-4 py-3 bg-green-600 text-white rounded-md hover:bg-green-700"
                  >
                    <CheckCircle size={18} />
                    <span>Complete</span>
                  </Button>
                </div>
              </div>
            ) : (
              <div className="text-center py-12">
                <Users className="mx-auto h-16 w-16 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Current Patient</h3>
                <p className="text-gray-600 mb-6">Call the next patient to start consultation</p>
                <Button
                  onClick={callNextPatient}
                  disabled={actionLoading || waitingPatients.length === 0}
                  className="flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 mx-auto"
                >
                  <Play size={18} />
                  <span>{actionLoading ? 'Calling...' : 'Call Next Patient'}</span>
                </Button>
                {waitingPatients.length === 0 && (
                  <p className="text-sm text-gray-500 mt-3">No patients waiting in queue</p>
                )}
              </div>
            )}
          </div>

          {/* Queue Section */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900">Waiting Queue</h2>
              <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                {waitingPatients.length} waiting
              </div>
            </div>

            {waitingPatients.length > 0 ? (
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {waitingPatients.map((patient, index) => (
                  <div
                    key={patient.queueId}
                    className={`p-4 rounded-lg border ${
                      index === 0 
                        ? 'border-blue-200 bg-blue-50' 
                        : 'border-gray-200 bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                          index === 0 
                            ? 'bg-blue-600 text-white' 
                            : 'bg-gray-400 text-white'
                        }`}>
                          {patient.queueNumber}
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{patient.patientName}</p>
                          <p className="text-sm text-gray-600">
                            Wait: {patient.estimatedWaitTime}min
                          </p>
                        </div>
                      </div>
                      {index === 0 && (
                        <div className="text-blue-600 text-sm font-medium">Next</div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <AlertCircle className="mx-auto h-12 w-12 text-gray-400 mb-3" />
                <p className="text-gray-600">No patients waiting in queue</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DoctorInterface;
