import React, { useEffect, useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import {
  deletePatientById,
  getPatientById,
  getPatientsPaginated, 
} from "../services/patientApis";
import { FaEdit, FaTrashAlt, FaEye, FaSearch, FaUsers, FaUserCheck, FaIdCard, FaPlus } from "react-icons/fa";
import PatientViewModal from "../components/patients/PatientViewModal";
import { showError, showSuccess } from "../utils/toastUtils";
import ConfirmDialog from "../utils/ConfirmDialog";
import { useRoles } from "../hooks/useRoles"; 

const debounce = (func: (...args: any[]) => void, delay: number) => {
  let timeoutId: ReturnType<typeof setTimeout>;
  return (...args: any[]) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

const PatientListPage = () => {
  const [patients, setPatients] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [page, setPage] = useState(0);
  const [pageSize] = useState(10);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPatient, setSelectedPatient] = useState<any>(null);
  const [showViewModal, setShowViewModal] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const [isConfirmOpen, setIsConfirmOpen] = useState(false);
  const [pendingDeleteId, setPendingDeleteId] = useState<string | null>(null);
  const [fullName, setFullName] = useState("");
  const { hasRole } = useRoles();
  const activePatientsCount = patients.filter(patient => patient.isActive).length;
  const abhaVerifiedPatients = patients.filter(
  (patient) => patient.abha?.abhaAddress && patient.abha.abhaAddress.trim() !== ""
);
const abhaVerifiedCount = abhaVerifiedPatients.length;

  const navigate = useNavigate();

  const fetchPatients = async (query: string, currentPage: number) => {
    const result = await getPatientsPaginated({
      query,
      page: currentPage,
      size: pageSize,
    });
    setPatients(result.results || []);
    setTotalCount(result.totalElements || 0);
  };

  const debouncedSearch = useRef(
    debounce((query: string) => {
      setPage(0);
      fetchPatients(query, 0);
    }, 400)
  ).current;

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    debouncedSearch(query);
  };

  const handleDeleteClick = (id: string) => {
    const fetchPatient = patients.find((x) => x.patientId === id);
    if (!fetchPatient) return;

    const name = `${fetchPatient.firstName} ${
      fetchPatient.middleName ? fetchPatient.middleName + " " : ""
    }${fetchPatient.lastName}`;
    setFullName(name);
    setPendingDeleteId(id);
    setIsConfirmOpen(true);
  };

  const confirmDelete = async () => {
    if (!pendingDeleteId) return;

    const success = await deletePatientById(pendingDeleteId);
    if (success) {
      fetchPatients(searchQuery, page);
      showSuccess("Patient Deleted Successfully", fullName);
    } else {
      showError("Failed To Delete Patient", fullName);
    }

    setIsConfirmOpen(false);
    setPendingDeleteId(null);
  };

  const handleView = async (id: string) => {
    try {
      const result = await getPatientById(id);
      if (Array.isArray(result) && result.length > 0) {
        setSelectedPatient(result[0]);
        setShowViewModal(true);
      } else {
        alert("Patient not found.");
      }
    } catch (error) {
      console.error("Error fetching patient details:", error);
      alert("Error fetching patient details.");
    }
  };

  useEffect(() => {
    const handleRefreshEvent = () => setRefreshTrigger((prev) => prev + 1);
    window.addEventListener("patient:registered", handleRefreshEvent);
    return () => window.removeEventListener("patient:registered", handleRefreshEvent);
  }, []);

  useEffect(() => {
    fetchPatients(searchQuery, page);
  }, [refreshTrigger, page]);

  const highlightText = (text: string, query: string) => {
    if (!query) return text;
    const regex = new RegExp(`(${query})`, "gi");
    return text.split(regex).map((part, index) =>
      part.toLowerCase() === query.toLowerCase() ? (
        <span key={index} className="bg-yellow-200 px-1 py-0.5 rounded font-semibold text-yellow-900">{part}</span>
      ) : (
        part
      )
    );
  };

  const totalPages = Math.ceil(totalCount / pageSize);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        
        {/* Header Section */}
        <div className="mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div className="mb-6 lg:mb-0">
              <h1 className="text-4xl font-bold text-gray-900 mb-2 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Patient Management
              </h1>
              <p className="text-gray-600 text-lg">Manage and monitor patient records efficiently</p>
            </div>
            
            {(hasRole("admin") || hasRole("receptionist")) && (
              <div className="flex-shrink-0">
                <button
                  className="group relative inline-flex items-center justify-center px-8 py-3 text-lg font-medium text-white transition-all duration-200 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl hover:from-blue-700 hover:to-purple-700 hover:scale-105 hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transform"
                  onClick={() => navigate("/patients")}
                >
                  <FaPlus className="w-5 h-5 mr-2 transition-transform group-hover:rotate-90" />
                  Register New Patient
                  <div className="absolute inset-0 bg-white opacity-20 rounded-xl blur-xl group-hover:opacity-30 transition-opacity"></div>
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {/* Total Patients Card */}
          <div className="group relative overflow-hidden bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-blue-200">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5"></div>
            <div className="relative p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-sm font-semibold text-gray-500 uppercase tracking-wide">Total Patients</p>
                  <h3 className="text-3xl font-bold text-gray-900">{totalCount.toLocaleString()}</h3>
                  <div className="flex items-center space-x-1">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                      </svg>
                      +12% from last month
                    </span>
                  </div>
                </div>
                <div className="relative">
                  <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <FaUsers className="w-8 h-8 text-white" />
                  </div>
                  <div className="absolute inset-0 bg-blue-400 rounded-2xl blur-lg opacity-30 group-hover:opacity-50 transition-opacity"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Active Patients Card */}
          <div className="group relative overflow-hidden bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-green-200">
            <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5"></div>
            <div className="relative p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-sm font-semibold text-gray-500 uppercase tracking-wide">Active Patients</p>
                  <h3 className="text-3xl font-bold text-gray-900">{activePatientsCount.toLocaleString()}</h3>
                  <p className="text-sm text-gray-600">Currently registered</p>
                </div>
                <div className="relative">
                  <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <FaUserCheck className="w-8 h-8 text-white" />
                  </div>
                  <div className="absolute inset-0 bg-green-400 rounded-2xl blur-lg opacity-30 group-hover:opacity-50 transition-opacity"></div>
                </div>
              </div>
            </div>
          </div>

          {/* ABHA Verified Card */}
          <div className="group relative overflow-hidden bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-purple-200">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-indigo-500/5"></div>
            <div className="relative p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-sm font-semibold text-gray-500 uppercase tracking-wide">ABHA Verified</p>
                  <h3 className="text-3xl font-bold text-gray-900">{abhaVerifiedCount.toLocaleString()}</h3>
                  <p className="text-sm text-gray-600">Digital health records</p>
                </div>
                <div className="relative">
                  <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <FaIdCard className="w-8 h-8 text-white" />
                  </div>
                  <div className="absolute inset-0 bg-purple-400 rounded-2xl blur-lg opacity-30 group-hover:opacity-50 transition-opacity"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Search Section */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 mb-8">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
              <FaSearch className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              value={searchQuery}
              onChange={handleSearchInputChange}
              placeholder="Search by firstname, lastname, email, phone number, or ID..."
              className="block w-full pl-12 pr-4 py-4 text-lg border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 hover:bg-white focus:bg-white"
            />
          </div>
        </div>

        {/* Patient Table */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
                <tr>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 uppercase tracking-wider">Name</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 uppercase tracking-wider">Phone</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 uppercase tracking-wider">Email</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 uppercase tracking-wider">Identifier</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 uppercase tracking-wider">Age</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 uppercase tracking-wider">Gender</th>
                  <th className="px-6 py-4 text-right text-sm font-semibold text-gray-900 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {patients.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="px-6 py-12 text-center">
                      <div className="flex flex-col items-center justify-center space-y-3">
                        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                          <FaUsers className="w-8 h-8 text-gray-400" />
                        </div>
                        <p className="text-lg font-medium text-gray-500">No patients found</p>
                        <p className="text-sm text-gray-400">Try adjusting your search criteria</p>
                      </div>
                    </td>
                  </tr>
                ) : (
                  patients.map((p: any, index) => (
                    <tr key={p.patientId} className={`hover:bg-gray-50 transition-colors duration-150 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'}`}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                              <span className="text-sm font-medium text-white">
                                {p.firstName?.[0]?.toUpperCase()}{p.lastName?.[0]?.toUpperCase()}
                              </span>
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-semibold text-gray-900">
                              {highlightText(`${p.firstName} ${p.middleName || ""} ${p.lastName}`, searchQuery)}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        {highlightText(p.phone || "N/A", searchQuery)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        {highlightText(p.email || "N/A", searchQuery)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        {highlightText(p.identifierNumber || "N/A", searchQuery)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                        {p.age ?? "N/A"}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          p.gender === 'Male' ? 'bg-blue-100 text-blue-800' : 
                          p.gender === 'Female' ? 'bg-pink-100 text-pink-800' : 
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {p.gender ?? "N/A"}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          {/* View Patient: All roles */}
                          {(hasRole("admin") || hasRole("doctor") || hasRole("receptionist")) && (
                            <button
                              className="group relative inline-flex items-center justify-center w-10 h-10 text-green-600 bg-green-50 rounded-lg hover:bg-green-100 hover:text-green-700 transition-all duration-200 hover:scale-105"
                              onClick={() => handleView(p.patientId)}
                              title="View Patient"
                            >
                              <FaEye className="w-4 h-4" />
                            </button>
                          )}

                          {/* Edit Patient: Admin + Receptionist only */}
                          {(hasRole("admin") || hasRole("receptionist")) && (
                            <button
                              className="group relative inline-flex items-center justify-center w-10 h-10 text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 hover:text-blue-700 transition-all duration-200 hover:scale-105"
                              onClick={() => navigate(`/patients/${p.patientId}`)}
                              title="Edit Patient"
                            >
                              <FaEdit className="w-4 h-4" />
                            </button>
                          )}

                          {/* Delete Patient: Admin only */}
                          {hasRole("admin") && (
                            <button
                              className="group relative inline-flex items-center justify-center w-10 h-10 text-red-600 bg-red-50 rounded-lg hover:bg-red-100 hover:text-red-700 transition-all duration-200 hover:scale-105"
                              onClick={() => handleDeleteClick(p.patientId)}
                              title="Delete Patient"
                            >
                              <FaTrashAlt className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Enhanced Pagination */}
        <div className="mt-8 flex items-center justify-between">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={page === 0}
              onClick={() => setPage((p) => Math.max(0, p - 1))}
            >
              Previous
            </button>
            <button
              className="relative inline-flex items-center px-4 py-2 ml-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={page + 1 >= totalPages}
              onClick={() => setPage((p) => p + 1)}
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing <span className="font-medium">{page * pageSize + 1}</span> to{" "}
                <span className="font-medium">{Math.min((page + 1) * pageSize, totalCount)}</span> of{" "}
                <span className="font-medium">{totalCount}</span> results
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                  disabled={page === 0}
                  onClick={() => setPage((p) => Math.max(0, p - 1))}
                >
                  Previous
                </button>
                <span className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border-t border-b border-gray-300">
                  Page {page + 1} of {totalPages}
                </span>
                <button
                  className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                  disabled={page + 1 >= totalPages}
                  onClick={() => setPage((p) => p + 1)}
                >
                  Next
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      {showViewModal && selectedPatient && (
        <PatientViewModal patient={selectedPatient} onClose={() => setShowViewModal(false)} />
      )}

      <ConfirmDialog
        isOpen={isConfirmOpen}
        message={`Are you sure you want to delete ${fullName}?`}
        onConfirm={confirmDelete}
        onCancel={() => setIsConfirmOpen(false)}
      />
    </div>
  );
};

export default PatientListPage;