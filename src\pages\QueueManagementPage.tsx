import React, { useState, useEffect } from "react";
import { <PERSON>, Clock, BarChart3, <PERSON><PERSON><PERSON>, Bell } from "lucide-react";
import { QueueManagement } from "../components/appointments/QueueManagement";
import { WaitTimeDisplay } from "../components/appointments/WaitTimeDisplay";
import { QueueSettings } from "../components/appointments/QueueSettings";
import { ServiceType } from "../types/queue";
import { Button } from "../commonfields/Button";
import { Select } from "../commonfields/Select";

export const QueueManagementPage: React.FC = () => {
  const [selectedFacility, setSelectedFacility] = useState("fac-001");
  const [showNotifications, setShowNotifications] = useState(true);
  const [showSettings, setShowSettings] = useState(false);

  // Set page title
  useEffect(() => {
    document.title = "Queue Management - MeghaSanjeevini";
    return () => {
      document.title = "MeghaSanjeevini Healthcare Platform";
    };
  }, []);

  // Mock facilities for demo
  const facilities = [
    { id: "fac-001", name: "City General Hospital" },
    { id: "fac-002", name: "Metro Medical Center" },
    { id: "fac-003", name: "Community Health Clinic" }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <div className="bg-indigo-100 p-3 rounded-full">
                <Users className="text-indigo-600" size={24} />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Queue Management System</h1>
                <p className="text-gray-600">Real-time patient queue monitoring and wait time estimation</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Facility Selector */}
              <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-gray-700">Facility:</label>
                <Select
                  value={selectedFacility}
                  onChange={(e) => setSelectedFacility(e.target.value)}
                  className="w-48"
                >
                  {facilities.map(facility => (
                    <option key={facility.id} value={facility.id}>
                      {facility.name}
                    </option>
                  ))}
                </Select>
              </div>

              {/* Notifications Toggle */}
              <Button
                onClick={() => setShowNotifications(!showNotifications)}
                className={`flex items-center space-x-2 px-3 py-2 rounded-md ${
                  showNotifications 
                    ? 'bg-indigo-600 text-white' 
                    : 'border border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                <Bell size={16} />
                <span>Notifications</span>
              </Button>

              {/* Settings */}
              <Button
                onClick={() => setShowSettings(true)}
                className="flex items-center space-x-2 px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
              >
                <Settings size={16} />
                <span>Settings</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Notifications Bar */}
      {showNotifications && (
        <div className="bg-blue-50 border-b border-blue-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
            <div className="flex items-center space-x-4">
              <Bell className="text-blue-600" size={16} />
              <div className="flex-1">
                <div className="flex items-center space-x-6 text-sm">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                    <span className="text-red-700">Emergency queue: 2 patients waiting</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <span className="text-yellow-700">Consultation wait time increased to 35 min</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-green-700">Laboratory service running on time</span>
                  </div>
                </div>
              </div>
              <Button
                onClick={() => setShowNotifications(false)}
                className="text-blue-600 hover:text-blue-800"
              >
                ×
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total in Queue</p>
                <p className="text-2xl font-bold text-gray-900">24</p>
              </div>
              <div className="bg-blue-100 p-3 rounded-full">
                <Users className="text-blue-600" size={20} />
              </div>
            </div>
            <div className="mt-2 text-sm text-gray-600">
              Across all services
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Wait Time</p>
                <p className="text-2xl font-bold text-yellow-600">22 min</p>
              </div>
              <div className="bg-yellow-100 p-3 rounded-full">
                <Clock className="text-yellow-600" size={20} />
              </div>
            </div>
            <div className="mt-2 text-sm text-gray-600">
              Facility average
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Processed Today</p>
                <p className="text-2xl font-bold text-green-600">156</p>
              </div>
              <div className="bg-green-100 p-3 rounded-full">
                <BarChart3 className="text-green-600" size={20} />
              </div>
            </div>
            <div className="mt-2 text-sm text-gray-600">
              +12% from yesterday
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Peak Hour</p>
                <p className="text-2xl font-bold text-purple-600">2-3 PM</p>
              </div>
              <div className="bg-purple-100 p-3 rounded-full">
                <Clock className="text-purple-600" size={20} />
              </div>
            </div>
            <div className="mt-2 text-sm text-gray-600">
              Highest volume
            </div>
          </div>
        </div>

        {/* Featured Wait Time Display */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Current Wait Times</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <WaitTimeDisplay 
              serviceType={ServiceType.Consultation} 
              facilityId={selectedFacility}
              showDetails={false}
            />
            <WaitTimeDisplay 
              serviceType={ServiceType.Emergency} 
              facilityId={selectedFacility}
              showDetails={false}
            />
            <WaitTimeDisplay 
              serviceType={ServiceType.Laboratory} 
              facilityId={selectedFacility}
              showDetails={false}
            />
          </div>
        </div>

        {/* Main Queue Management Component */}
        <QueueManagement 
          facilityId={selectedFacility}
          showStatistics={true}
        />
      </div>

      {/* Footer */}
      <div className="bg-white border-t border-gray-200 mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              © 2024 MeghaSanjeevini Healthcare Platform. Queue Management System v1.0
            </div>
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <span>Last updated: {new Date().toLocaleTimeString()}</span>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>System Online</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Settings Modal */}
      <QueueSettings
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
      />
    </div>
  );
};
