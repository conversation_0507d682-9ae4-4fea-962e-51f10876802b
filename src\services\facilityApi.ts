import axios from "axios";

const BASE_URL = import.meta.env.VITE_API_BASE_URL;
// const BASE_URL = "https://megha-dev.sirobilt.com/api";

// Fetch facilities by name
export const fetchFacilities = async (name: string) => {
  try {
    const searchName = name.trim() || "a"; // fallback search
    const params = new URLSearchParams({ name: searchName, page: "0", size: "10" });
    const response = await axios.get(`${BASE_URL}/api/facilities/suggest?${params}`);
    return response.data.results || [];
  } catch (error) {
    console.error("Error fetching facilities:", error);
    return [];
  }
};

// Fetch facility label by ID
export const fetchFacilityLabel = async (value: string) => {
  if (!value || value.trim() === "") return "";

  try {
    const params = new URLSearchParams({ name: "a", page: "0", size: "50" });
    const response = await axios.get(`${BASE_URL}/api/facilities/suggest?${params}`);
    const found = response.data.results?.find(
      (f: { hospitalId: string }) => f.hospitalId === value
    );
    return found?.facilityName || "";
  } catch (error) {
    console.error("Error fetching facility label:", error);
    return "";
  }
};
