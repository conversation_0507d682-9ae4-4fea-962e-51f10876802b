import axios from "axios";
import type { OptionalPatientRegistrationPayload, PatientRegistrationPayload } from '../types/patient';

// Backend API Configuration - Updated to match appointment API
// const BASE_URL = "https://megha-dev.sirobilt.com";
const BASE_URL = import.meta.env.VITE_API_BASE_URL;

// Configure axios defaults
axios.defaults.headers.common['Content-Type'] = 'application/json';
axios.defaults.headers.common['Accept'] = 'application/json';

// Add request interceptor for authentication
axios.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('authToken') || localStorage.getItem('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add facility context if available
    const facilityId = localStorage.getItem('selectedFacilityId');
    if (facilityId) {
      config.headers['X-Facility-Id'] = facilityId;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
axios.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('Patient API Error:', {
      status: error.response?.status,
      message: error.response?.data?.message || error.message,
      data: error.response?.data,
      url: error.config?.url
    });

    // Handle authentication errors
    if (error.response?.status === 401) {
      localStorage.removeItem('authToken');
      localStorage.removeItem('accessToken');
    }

    return Promise.reject(error);
  }
);

// Patient DTO interface for API responses
interface PatientDTO {
  patientId: string;
  upId?: string;
  facilityId?: string;
  identifierType?: string;
  identifierNumber?: string;
  title?: string;
  firstName?: string;
  middleName?: string;
  lastName?: string;
  dateOfBirth?: string;
  age?: number;
  gender?: string;
  bloodGroup?: string;
  maritalStatus?: string;
  citizenship?: string;
  religion?: string;
  caste?: string;
  occupation?: string;
  education?: string;
  annualIncome?: string;
  contacts?: any[];
  addresses?: any[];
  abha?: any;
  billingReferral?: any;
  emergencyContacts?: any[];
  informationSharing?: any;
  insurance?: any;
  referrals?: any[];
  relationships?: any[];
  tokens?: any[];
  createdAt?: string;
  updatedAt?: string;
  isActive:boolean;
}

// API Response interfaces
interface ApiResponsePatientDTO {
  success: boolean;
  data: PatientDTO;
  message?: string;
  error?: string;
}

interface PaginatedPatientResponse {
  content: PatientDTO[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
  numberOfElements: number;
}



// Helper function to transform patient data
const transformPatientData = (dto: PatientDTO): PatientRegistrationPayload => {
  if (!dto) {
    console.error("transformPatientData: dto is null or undefined");
    throw new Error("Invalid patient data");
  }

  return {
    patientId: dto.patientId,
    upId: dto.upId,
    facilityId: dto.facilityId,
    identifierType: dto.identifierType as any,
    identifierNumber: dto.identifierNumber,
    title: dto.title as any,
    firstName: dto.firstName,
    middleName: dto.middleName,
    lastName: dto.lastName,
    dateOfBirth: dto.dateOfBirth,
    age: dto.age,
    gender: dto.gender as any,
    bloodGroup: dto.bloodGroup as any,
    maritalStatus: dto.maritalStatus as any,
    citizenship: dto.citizenship,
    religion: dto.religion,
    caste: dto.caste,
    occupation: dto.occupation,
    education: dto.education,
    annualIncome: dto.annualIncome,
    contacts: dto.contacts || [],
    addresses: dto.addresses || [],
    abha: dto.abha,
    billingReferral: dto.billingReferral,
    emergencyContacts: dto.emergencyContacts || [],
    informationSharing: dto.informationSharing,
    insurance: dto.insurance,
    referrals: dto.referrals || [],
    relationships: dto.relationships || [],
    tokens: dto.tokens || [],
    isActive:dto.isActive||false,
  
  };
};

// POST /api/patients - Create new patient
export const createPatient = async (payload: OptionalPatientRegistrationPayload) => {
  try {
    console.log("Creating patient with payload:", payload);

    const response = await axios.post(`${BASE_URL}/api/patients`, payload);

    console.log("Create patient response:", response.data);

    // Handle different response formats
    let patientData = null;

    if (response.data?.data) {
      // Response format: { success: true, data: PatientDTO, message: "..." }
      patientData = response.data.data;
    } else if (response.data?.patientId) {
      // Direct PatientDTO response
      patientData = response.data;
    } else {
      // Unknown format, log and return error
      console.error("Unexpected response format:", response.data);
      return {
        success: false,
        error: "Unexpected response format from server"
      };
    }

    const patient = transformPatientData(patientData);
    return {
      success: true,
      data: patient,
      message: response.data.message || "Patient created successfully"
    };
  } catch (error: any) {
    console.error("Create Patient Error:", error.response?.data || error.message);

    const errorMessage = error.response?.data?.message ||
                        error.response?.data?.error ||
                        error.message ||
                        "Failed to create patient";

    return {
      success: false,
      error: errorMessage,
      details: error.response?.data
    };
  }
};


export const getPatientById = async (id: string) => {
  try {
    const response = await axios.get(`${BASE_URL}/api/patients`, {
      params: { upId:id },
    });
    return response.data;
  } catch (error: any) {
    console.error("Fetch Error:", error);
    throw new Error("Failed to fetch patient data");
  }
};

// PUT /api/patients/{id} - Update patient
export const updatePatient = async (id: string, payload: OptionalPatientRegistrationPayload) => {
  try {
    console.log("Updating patient:", id, payload);

    const response = await axios.put(`${BASE_URL}/api/patients/${id}`, payload);

    console.log("Update patient response:", response.data);

    // Handle different response formats
    let patientData = null;

    if (response.data?.data) {
      // Response format: { success: true, data: PatientDTO, message: "..." }
      patientData = response.data.data;
    } else if (response.data?.patientId) {
      // Direct PatientDTO response
      patientData = response.data;
    } else {
      return {
        success: false,
        error: "Unexpected response format from server"
      };
    }

    const patient = transformPatientData(patientData);

    return {
      success: true,
      data: patient,
      message: response.data.message || "Patient updated successfully"
    };
  } catch (error: any) {
    console.error("Update Patient Error:", error.response?.data || error.message);

    const errorMessage = error.response?.data?.message ||
                        error.response?.data?.error ||
                        error.message ||
                        "Failed to update patient";

    return {
      success: false,
      error: errorMessage,
      details: error.response?.data
    };
  }
};

export const getAllPatients = async () => {
  try {
    const res = await axios.get(`${BASE_URL}/api/patients`);
    return res.data;
  } catch (err: any) {
    console.error("Failed to fetch patients:", err);
    return [];
  }
};

// DELETE /api/patients/{id} - Delete patient
export const deletePatient = async (id: string) => {
  try {
    console.log("Deleting patient:", id);

    await axios.delete(`${BASE_URL}/api/patients/${id}`);

    return {
      success: true,
      message: "Patient deleted successfully"
    };
  } catch (error: any) {
    console.error("Delete Patient Error:", error.response?.data || error.message);

    const errorMessage = error.response?.data?.message ||
                        error.response?.data?.error ||
                        error.message ||
                        "Failed to delete patient";

    return {
      success: false,
      error: errorMessage,
      details: error.response?.data
    };
  }
};

// GET /api/patients - Search patients with filters (using query params)
export const searchPatients = async (filters: Record<string, string>): Promise<PatientRegistrationPayload[]> => {
  try {
    console.log("Searching patients with filters:", filters);

    const cleanedFilters: Record<string, string> = {};

    Object.entries(filters).forEach(([key, value]) => {
      const trimmed = value?.trim();
      if (trimmed) {
        cleanedFilters[key] = trimmed;
      }
    });

    const params = new URLSearchParams(cleanedFilters);
    const response = await axios.get(`${BASE_URL}/api/patients?${params}`);

    console.log("Search patients response:", response.data);

    // Handle different response formats
    let patientsData = [];

    if (Array.isArray(response.data)) {
      // Direct array response
      patientsData = response.data;
    } else if (response.data?.data && Array.isArray(response.data.data)) {
      // Response format: { success: true, data: PatientDTO[], message: "..." }
      patientsData = response.data.data;
    } else if (response.data?.content && Array.isArray(response.data.content)) {
      // Paginated response format
      patientsData = response.data.content;
    } else {
      console.warn("Unexpected response format for searchPatients:", response.data);
      return [];
    }

    return patientsData.map(transformPatientData);
  } catch (error: any) {
    console.error("Search patients failed:", error.response?.data || error.message);
    return [];
  }
};


// DELETE /api/patients/{id} - Alternative delete function (alias)
export const deletePatientById = async (id: string): Promise<boolean> => {
  try {
    const result = await deletePatient(id);
    return result.success;
  } catch (error) {
    console.error("Delete patient by ID failed:", error);
    return false;
  }
};

// GET /api/patients - Get patients with pagination
export const getPatientsPaginated = async ({
  page,
  size,
  query,
}: {
  page: number;
  size: number;
  query: string;
}) => {
  try {
    console.log("Fetching paginated patients:", { page, size, query });

    const params = new URLSearchParams({
      page: page.toString(),
      size: size.toString(),
    });

    // Add query parameter if provided
    if (query && query.trim()) {
      params.append('query', query.trim());
    }

    const response = await axios.get(`${BASE_URL}/api/patients?${params}`);

    console.log("Paginated patients response:", response.data);

    const responseData = response.data;

    // Handle different response formats
    let patients = [];
    let totalElements = 0;
    let totalPages = 0;
    let currentPage = page;
    let pageSize = size;
    let isFirst = true;
    let isLast = true;
    let numberOfElements = 0;

    if (responseData?.content && Array.isArray(responseData.content)) {
      // Spring Boot pageable response format
      patients = responseData.content.map(transformPatientData);
      totalElements = responseData.totalElements || 0;
      totalPages = responseData.totalPages || 0;
      currentPage = responseData.number || page;
      pageSize = responseData.size || size;
      isFirst = responseData.first !== undefined ? responseData.first : page === 0;
      isLast = responseData.last !== undefined ? responseData.last : true;
      numberOfElements = responseData.numberOfElements || patients.length;
    } else if (Array.isArray(responseData)) {
      // Simple array response - simulate pagination
      patients = responseData.map(transformPatientData);
      totalElements = patients.length;
      totalPages = Math.ceil(totalElements / size);
      numberOfElements = patients.length;
      isFirst = page === 0;
      isLast = page >= totalPages - 1;
    } else if (responseData?.data && Array.isArray(responseData.data)) {
      // Wrapped array response
      patients = responseData.data.map(transformPatientData);
      totalElements = patients.length;
      totalPages = Math.ceil(totalElements / size);
      numberOfElements = patients.length;
      isFirst = page === 0;
      isLast = page >= totalPages - 1;
    } else {
      console.warn("Unexpected response format for paginated patients:", responseData);
    }

    return {
      results: patients,
      content: patients, // For backward compatibility
      totalElements,
      totalPages,
      page: currentPage,
      size: pageSize,
      first: isFirst,
      last: isLast,
      numberOfElements
    };
  } catch (error: any) {
    console.error("Paginated fetch error:", error.response?.data || error.message);

    // Return empty result set on error
    return {
      results: [],
      content: [],
      totalElements: 0,
      totalPages: 0,
      page: 0,
      size: 20,
      first: true,
      last: true,
      numberOfElements: 0,
      error: error.response?.data?.message || error.message
    };
  }
};

// Additional utility functions for patient management

// Get patients by facility
export const getPatientsByFacility = async (facilityId: string): Promise<PatientRegistrationPayload[]> => {
  try {
    console.log("Fetching patients by facility:", facilityId);

    const params = new URLSearchParams({ facilityId });
    const response = await axios.get(`${BASE_URL}/api/patients?${params}`);

    console.log("Patients by facility response:", response.data);

    // Handle different response formats
    let patientsData = [];

    if (Array.isArray(response.data)) {
      // Direct array response
      patientsData = response.data;
    } else if (response.data?.data && Array.isArray(response.data.data)) {
      // Response format: { success: true, data: PatientDTO[], message: "..." }
      patientsData = response.data.data;
    } else if (response.data?.content && Array.isArray(response.data.content)) {
      // Paginated response format
      patientsData = response.data.content;
    } else {
      console.warn("Unexpected response format for patients by facility:", response.data);
      return [];
    }

    return patientsData.map(transformPatientData);
  } catch (error: any) {
    console.error("Failed to fetch patients by facility:", error.response?.data || error.message);
    return [];
  }
};

// Cache for patient data to prevent duplicate API calls
let patientCache: PatientRegistrationPayload[] | null = null;
let lastFetchTime = 0;
const CACHE_DURATION = 30000; // 30 seconds

// Clear patient cache (useful when new patients are added)
export const clearPatientCache = () => {
  patientCache = null;
  lastFetchTime = 0;
};

// Search patients for appointment dropdown (optimized for UI)
export const searchPatientsForDropdown = async (searchTerm: string = ""): Promise<PatientRegistrationPayload[]> => {
  try {
    // Check cache first
    const now = Date.now();
    if (patientCache && (now - lastFetchTime) < CACHE_DURATION) {
      console.log("✅ Returning cached patient data");
      return patientCache;
    }

    console.log("=== SEARCHING PATIENTS FOR DROPDOWN ===");
    console.log("Search term:", searchTerm);
    console.log("Base URL:", BASE_URL);

    // First try to get all patients
    console.log("Step 1: Attempting to fetch all patients...");
    const allPatients = await getAllPatients();

    console.log("getAllPatients result:", allPatients);
    console.log("getAllPatients length:", allPatients?.length);

    if (allPatients && allPatients.length > 0) {
      console.log(`✅ Successfully fetched ${allPatients.length} patients via getAllPatients`);
      // Cache the result
      patientCache = allPatients;
      lastFetchTime = now;
      return allPatients;
    }

    console.log("❌ No patients from getAllPatients, trying alternative approaches...");

    // If no patients from getAllPatients, try paginated approach
    console.log("Step 2: Trying paginated approach...");
    const result = await getPatientsPaginated({ page: 0, size: 100, query: searchTerm });

    console.log("Paginated result:", result);

    if (result && result.results && result.results.length > 0) {
      console.log(`✅ Successfully fetched ${result.results.length} patients via pagination`);
      return result.results;
    }

    console.log("❌ No patients from pagination either");

    // If still no patients, try search with empty filters
    console.log("Step 3: Trying search with empty filters...");
    const searchResult = await searchPatients({});

    console.log("Search result:", searchResult);

    if (searchResult && searchResult.length > 0) {
      console.log(`✅ Successfully fetched ${searchResult.length} patients via search`);
      return searchResult;
    }

    console.log("❌ No patients found through any method");
    console.log("=== END PATIENT SEARCH ===");
    return [];

  } catch (error: any) {
    console.error("❌ Failed to search patients for dropdown:", error);
    console.error("Error details:", {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data
    });
    return [];
  }
};

// Test function to verify API connectivity
export const testPatientAPI = async () => {
  try {
    console.log("=== TESTING PATIENT API CONNECTIVITY ===");
    console.log("Testing URL:", `${BASE_URL}/api/patients`);

    // Test basic GET request
    const response = await axios.get(`${BASE_URL}/api/patients`);
    console.log("✅ Patient API test response status:", response.status);
    console.log("✅ Patient API test response data:", response.data);
    console.log("✅ Response data type:", typeof response.data);
    console.log("✅ Is array?", Array.isArray(response.data));

    if (response.data) {
      console.log("✅ Response keys:", Object.keys(response.data));
      if (Array.isArray(response.data)) {
        console.log("✅ Array length:", response.data.length);
        if (response.data.length > 0) {
          console.log("✅ First item:", response.data[0]);
        }
      }
    }

    return {
      success: true,
      status: response.status,
      data: response.data,
      message: "Patient API is accessible"
    };
  } catch (error: any) {
    console.error("❌ Patient API test failed:", error.response?.data || error.message);
    console.error("❌ Error status:", error.response?.status);
    console.error("❌ Error config:", error.config);

    return {
      success: false,
      status: error.response?.status,
      error: error.response?.data || error.message,
      message: "Patient API is not accessible"
    };
  }
};

// Debug function to test all patient API methods
export const debugPatientAPI = async () => {
  console.log("=== DEBUGGING ALL PATIENT API METHODS ===");

  // Test API connectivity
  const apiTest = await testPatientAPI();
  console.log("API Test Result:", apiTest);

  // Test getAllPatients
  console.log("\n--- Testing getAllPatients ---");
  const allPatients = await getAllPatients();
  console.log("getAllPatients result:", allPatients);

  // Test searchPatientsForDropdown
  console.log("\n--- Testing searchPatientsForDropdown ---");
  const dropdownPatients = await searchPatientsForDropdown();
  console.log("searchPatientsForDropdown result:", dropdownPatients);

  // Test getPatientsPaginated
  console.log("\n--- Testing getPatientsPaginated ---");
  const paginatedPatients = await getPatientsPaginated({ page: 0, size: 10, query: "" });
  console.log("getPatientsPaginated result:", paginatedPatients);

  console.log("=== END DEBUG ===");

  return {
    apiTest,
    allPatients,
    dropdownPatients,
    paginatedPatients
  };
};

// Make debug function available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).debugPatientAPI = debugPatientAPI;
  (window as any).testPatientAPI = testPatientAPI;
}









