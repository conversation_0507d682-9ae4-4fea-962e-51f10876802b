// Utility functions for formatting various data types

export const formatPhoneNumber = (value: string): string => {
  // Remove all non-digits
  const digits = value.replace(/\D/g, '');

  // Limit to 10 digits
  const limitedDigits = digits.slice(0, 10);

  // Format as XXX-XXX-XXXX
  if (limitedDigits.length >= 6) {
    return `${limitedDigits.slice(0, 3)}-${limitedDigits.slice(3, 6)}-${limitedDigits.slice(6)}`;
  } else if (limitedDigits.length >= 3) {
    return `${limitedDigits.slice(0, 3)}-${limitedDigits.slice(3)}`;
  }

  return limitedDigits;
};

export const formatPincode = (value: string): string => {
  // Remove all non-digits and limit to 6 digits
  return value.replace(/\D/g, '').slice(0, 6);
};

export const formatABHA = (value: string): string => {
  // Remove all non-digits
  const digits = value.replace(/\D/g, '');

  // Limit to 14 digits
  const limitedDigits = digits.slice(0, 14);

  // Format as 12-1234-1234-1234
  if (limitedDigits.length >= 10) {
    return `${limitedDigits.slice(0, 2)}-${limitedDigits.slice(2, 6)}-${limitedDigits.slice(6, 10)}-${limitedDigits.slice(10)}`;
  } else if (limitedDigits.length >= 6) {
    return `${limitedDigits.slice(0, 2)}-${limitedDigits.slice(2, 6)}-${limitedDigits.slice(6)}`;
  } else if (limitedDigits.length >= 2) {
    return `${limitedDigits.slice(0, 2)}-${limitedDigits.slice(2)}`;
  }

  return limitedDigits;
};

export const formatAadhaar = (value: string): string => {
  // Remove all non-digits
  const digits = value.replace(/\D/g, '');

  // Limit to 12 digits
  const limitedDigits = digits.slice(0, 12);

  // Format as 1234-5678-9012
  if (limitedDigits.length >= 8) {
    return `${limitedDigits.slice(0, 4)}-${limitedDigits.slice(4, 8)}-${limitedDigits.slice(8)}`;
  } else if (limitedDigits.length >= 4) {
    return `${limitedDigits.slice(0, 4)}-${limitedDigits.slice(4)}`;
  }

  return limitedDigits;
};

export const formatIdentifierNumber = (value: string, identifierType: string): string => {
  switch (identifierType) {
    case 'ABHA':
      return formatABHA(value);
    case 'Aadhar':
      return formatAadhaar(value);
    case 'PAN':
      // PAN should be uppercase
      return value.toUpperCase().slice(0, 10);
    case 'Passport':
      // Passport should be uppercase
      return value.toUpperCase().slice(0, 8);
    case 'Driving_License':
      // Driving License should be uppercase
      return value.toUpperCase().slice(0, 15);
    default:
      return value.slice(0, 20);
  }
};

// Helper function to remove formatting for validation
export const removeFormatting = (value: string): string => {
  return value.replace(/[-\s()]/g, '');
};
