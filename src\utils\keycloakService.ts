import axios from "axios";

const REALM = "hospital-app"; // your realm
const CLIENT_ID = "react-client"; // your client ID
const CLIENT_SECRET = "D57dGJyfTHOvJo5rilmlCpADv3WyOjZA"; // your client secret

export async function loginWithKeycloak(username: string, password: string) {
  const params = new URLSearchParams();
  params.append("grant_type", "password");
  params.append("client_id", CLIENT_ID);
  params.append("client_secret", CLIENT_SECRET);
  params.append("username", username);
  params.append("password", password);

  try {
   const response = await axios.post(
  `https://megha-dev.sirobilt.com/auth/realms/${REALM}/protocol/openid-connect/token`,
    params,
    { headers: { "Content-Type": "application/x-www-form-urlencoded" } }
  );
    return response.data; // contains access_token, refresh_token, etc.
  } catch (error: any) {
    throw new Error(
      error.response?.data?.error_description || "Lo<PERSON> failed"
    );
  }
}
