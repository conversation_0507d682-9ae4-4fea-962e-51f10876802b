import { AddressType, IdentifierType, Gender, Title } from "../types/patientenums";
import type { PatientRegistrationPayload } from "../types/patient";
 
export function mapAbhaProfileToPatient(abhaProfile: any): Partial<PatientRegistrationPayload> {
 
 
  const gender = abhaProfile.gender === "M"
    ? Gender.Male
    : abhaProfile.gender === "F"
      ? Gender.Female
      : Gender.Other;
 
  const title = gender === Gender.Male
    ? Title.Mr
    : gender === Gender.Female
    ? Title.Ms
    : Title.Other;
 
  return {
    identifierType: IdentifierType.ABHA,
    identifierNumber: abhaProfile.ABHANumber,
 
    firstName: abhaProfile.firstName || null,
    middleName: abhaProfile.middleName || null,
    lastName: abhaProfile.lastName || null,
    dateOfBirth: getStandardDob(abhaProfile),
    gender,
    title,
    contacts: [
      {
        phoneNumber: abhaProfile.mobile || null,
        email: abhaProfile.email || null,
      },
    ],
 
    addresses: [
      {
        addressType: AddressType.Permanent,
        houseNoOrFlatNo: abhaProfile.address || null,
        districtId: abhaProfile.districtName || null,
        stateId: abhaProfile.stateName || null,
        // cityOrVillage: abhaProfile.districtName || null,
        pincode: abhaProfile.pinCode || null,
        country: "India",
      },
    ],
 
    abha: {
      abhaNumber: abhaProfile.ABHANumber || null,
      abhaAddress: abhaProfile.preferredAbhaAddress||abhaProfile.phrAddress?.[0] || null,
    },
  };
}
 
 
function getStandardDob(profile: any): string | null {
  // Case 1: dob string in format "DD-MM-YYYY"
  if (profile.dob) {
    const [dd, mm, yyyy] = profile.dob.split("-");
    if (dd && mm && yyyy) return `${yyyy}-${mm}-${dd}`;
  }
 
  // Case 2: Separate fields (as seen in ABHA login by number)
  if (profile.yearOfBirth && profile.monthOfBirth && profile.dayOfBirth) {
    return `${profile.yearOfBirth}-${pad(profile.monthOfBirth)}-${pad(profile.dayOfBirth)}`;
  }
 
  return null;
}
 
function pad(value: string | number): string {
  return value.toString().padStart(2, "0");
}