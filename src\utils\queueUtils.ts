import { QueueEntry, QueueStatus, ServiceType } from '../types/queue';
import { AppointmentPriority } from '../types/appointmentenums';

// Calculate estimated wait time based on queue position and service times
export const calculateEstimatedWaitTime = (
  queuePosition: number,
  averageServiceTime: number,
  currentServiceStartTime?: string
): number => {
  let waitTime = 0;
  
  // If there's a current service in progress, calculate remaining time
  if (currentServiceStartTime) {
    const startTime = new Date(currentServiceStartTime);
    const now = new Date();
    const elapsedMinutes = Math.floor((now.getTime() - startTime.getTime()) / (1000 * 60));
    const remainingTime = Math.max(0, averageServiceTime - elapsedMinutes);
    waitTime += remainingTime;
  }
  
  // Add time for patients ahead in queue
  waitTime += (queuePosition - 1) * averageServiceTime;
  
  return Math.max(0, waitTime);
};

// Sort queue entries by priority and queue number
export const sortQueueEntries = (entries: QueueEntry[]): QueueEntry[] => {
  const priorityOrder = {
    [AppointmentPriority.Emergency]: 0,
    [AppointmentPriority.Urgent]: 1,
    [AppointmentPriority.High]: 2,
    [AppointmentPriority.Normal]: 3,
    [AppointmentPriority.Low]: 4
  };

  return [...entries].sort((a, b) => {
    // First sort by priority
    const priorityDiff = priorityOrder[a.priority] - priorityOrder[b.priority];
    if (priorityDiff !== 0) return priorityDiff;
    
    // Then by queue number (first come, first served within same priority)
    return a.queueNumber - b.queueNumber;
  });
};

// Get queue statistics for a service
export const getQueueStats = (entries: QueueEntry[]) => {
  const total = entries.length;
  const waiting = entries.filter(e => e.status === QueueStatus.Waiting).length;
  const called = entries.filter(e => e.status === QueueStatus.Called).length;
  const inService = entries.filter(e => e.status === QueueStatus.InService).length;
  const completed = entries.filter(e => e.status === QueueStatus.Completed).length;
  const noShow = entries.filter(e => e.status === QueueStatus.NoShow).length;
  
  const emergency = entries.filter(e => 
    e.priority === AppointmentPriority.Emergency || 
    e.priority === AppointmentPriority.Urgent
  ).length;
  
  return {
    total,
    waiting,
    called,
    inService,
    completed,
    noShow,
    emergency,
    activeQueue: waiting + called + inService
  };
};

// Calculate average wait time from completed entries
export const calculateAverageWaitTime = (completedEntries: QueueEntry[]): number => {
  const waitTimes = completedEntries
    .filter(entry => entry.actualWaitTime !== undefined)
    .map(entry => entry.actualWaitTime!);
  
  if (waitTimes.length === 0) return 0;
  
  return Math.round(waitTimes.reduce((sum, time) => sum + time, 0) / waitTimes.length);
};

// Calculate average service time from completed entries
export const calculateAverageServiceTime = (completedEntries: QueueEntry[]): number => {
  const serviceTimes = completedEntries
    .filter(entry => entry.actualServiceTime !== undefined)
    .map(entry => entry.actualServiceTime!);
  
  if (serviceTimes.length === 0) return 15; // Default 15 minutes
  
  return Math.round(serviceTimes.reduce((sum, time) => sum + time, 0) / serviceTimes.length);
};

// Get next patient to be called
export const getNextPatient = (entries: QueueEntry[]): QueueEntry | null => {
  const waitingEntries = entries.filter(e => e.status === QueueStatus.Waiting);
  const sortedEntries = sortQueueEntries(waitingEntries);
  return sortedEntries.length > 0 ? sortedEntries[0] : null;
};

// Check if service is in peak hours
export const isServiceInPeakHours = (peakHours: string[]): boolean => {
  const now = new Date();
  const currentHour = now.getHours();
  const currentTime = `${currentHour.toString().padStart(2, '0')}:00`;
  
  return peakHours.some(peak => {
    const [start, end] = peak.split('-');
    const startHour = parseInt(start.split(':')[0]);
    const endHour = parseInt(end.split(':')[0]);
    return currentHour >= startHour && currentHour < endHour;
  });
};

// Format time duration
export const formatDuration = (minutes: number): string => {
  if (minutes < 60) {
    return `${minutes} min`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (remainingMinutes === 0) {
    return `${hours}h`;
  }
  
  return `${hours}h ${remainingMinutes}m`;
};

// Get service type color
export const getServiceTypeColor = (serviceType: ServiceType): string => {
  switch (serviceType) {
    case ServiceType.Emergency:
      return 'red';
    case ServiceType.Consultation:
      return 'blue';
    case ServiceType.Laboratory:
      return 'green';
    case ServiceType.Pharmacy:
      return 'purple';
    case ServiceType.Diagnostic:
      return 'orange';
    case ServiceType.Radiology:
      return 'indigo';
    case ServiceType.Registration:
      return 'teal';
    case ServiceType.Procedure:
      return 'cyan';
    default:
      return 'gray';
  }
};

// Get priority level number for calculations
export const getPriorityLevel = (priority: AppointmentPriority): number => {
  switch (priority) {
    case AppointmentPriority.Emergency:
      return 5;
    case AppointmentPriority.Urgent:
      return 4;
    case AppointmentPriority.High:
      return 3;
    case AppointmentPriority.Normal:
      return 2;
    case AppointmentPriority.Low:
      return 1;
    default:
      return 2;
  }
};

// Calculate confidence score for wait time estimation
export const calculateConfidenceScore = (
  queueLength: number,
  historicalAccuracy: number,
  timeOfDay: number
): number => {
  let confidence = 100;
  
  // Reduce confidence for longer queues
  if (queueLength > 10) confidence -= 20;
  else if (queueLength > 5) confidence -= 10;
  
  // Reduce confidence during peak hours
  if (timeOfDay >= 10 && timeOfDay <= 12) confidence -= 15; // Morning peak
  if (timeOfDay >= 14 && timeOfDay <= 16) confidence -= 15; // Afternoon peak
  
  // Factor in historical accuracy
  confidence = Math.round(confidence * (historicalAccuracy / 100));
  
  return Math.max(50, Math.min(100, confidence)); // Keep between 50-100%
};

// Generate queue number for new entry
export const generateQueueNumber = (existingEntries: QueueEntry[], serviceType: ServiceType): number => {
  const serviceEntries = existingEntries.filter(entry => 
    entry.serviceType === serviceType && 
    entry.status !== QueueStatus.Completed &&
    entry.status !== QueueStatus.NoShow
  );
  
  if (serviceEntries.length === 0) return 1;
  
  const maxNumber = Math.max(...serviceEntries.map(entry => entry.queueNumber));
  return maxNumber + 1;
};

// Check if patient can be fast-tracked based on priority
export const canFastTrack = (priority: AppointmentPriority): boolean => {
  return priority === AppointmentPriority.Emergency || priority === AppointmentPriority.Urgent;
};

// Get estimated service completion time
export const getEstimatedCompletionTime = (
  currentTime: Date,
  waitTime: number,
  serviceTime: number
): string => {
  const completionTime = new Date(currentTime.getTime() + (waitTime + serviceTime) * 60000);
  return completionTime.toLocaleTimeString('en-US', { 
    hour: '2-digit', 
    minute: '2-digit',
    hour12: false 
  });
};

// Validate queue entry data
export const validateQueueEntry = (entry: Partial<QueueEntry>): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (!entry.patientId) errors.push('Patient ID is required');
  if (!entry.patientName) errors.push('Patient name is required');
  if (!entry.serviceType) errors.push('Service type is required');
  if (!entry.priority) errors.push('Priority is required');
  if (!entry.facilityId) errors.push('Facility ID is required');
  
  return {
    isValid: errors.length === 0,
    errors
  };
};
